@echo off
echo =====================================
echo PACE v1.1.4 - Team Distribution Builder
echo =====================================
echo Creating a single file for your team...
echo.

REM Check if we're in the right directory
if not exist "main.py" (
    echo Error: main.py not found!
    echo Please run this script from the PACE root directory.
    pause
    exit /b 1
)

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python not found. Please install Python 3.9+ first.
    pause
    exit /b 1
)

echo Step 1: Building PACE executable...
echo.

REM Build the executable
python build_executable.py

REM Check if build was successful
if not exist "dist\PACE.exe" (
    echo Build failed! Please check the errors above.
    pause
    exit /b 1
)

echo.
echo Step 2: Creating team distribution...
echo.

REM Create team folder
if not exist "team_distribution" mkdir team_distribution

REM Copy the executable
copy "dist\PACE.exe" "team_distribution\" >nul

REM Create simple instructions
echo PACE v1.1.4 - Ready to Use! > "team_distribution\HOW_TO_USE.txt"
echo ============================= >> "team_distribution\HOW_TO_USE.txt"
echo. >> "team_distribution\HOW_TO_USE.txt"
echo QUICK START: >> "team_distribution\HOW_TO_USE.txt"
echo 1. Double-click PACE.exe >> "team_distribution\HOW_TO_USE.txt"
echo 2. Start creating documents! >> "team_distribution\HOW_TO_USE.txt"
echo. >> "team_distribution\HOW_TO_USE.txt"
echo FEATURES: >> "team_distribution\HOW_TO_USE.txt"
echo - DFIR SOWs >> "team_distribution\HOW_TO_USE.txt"
echo - TACI SOWs >> "team_distribution\HOW_TO_USE.txt"
echo - BEC SOWs >> "team_distribution\HOW_TO_USE.txt"
echo - Recovery and Remediation SOWs >> "team_distribution\HOW_TO_USE.txt"
echo - MSAs and BAAs >> "team_distribution\HOW_TO_USE.txt"
echo - Custom DPAs >> "team_distribution\HOW_TO_USE.txt"
echo. >> "team_distribution\HOW_TO_USE.txt"
echo REQUIREMENTS: >> "team_distribution\HOW_TO_USE.txt"
echo - Windows 10 or later >> "team_distribution\HOW_TO_USE.txt"
echo - No other software needed! >> "team_distribution\HOW_TO_USE.txt"
echo. >> "team_distribution\HOW_TO_USE.txt"
echo SUPPORT: >> "team_distribution\HOW_TO_USE.txt"
echo If you have any issues, contact the development team. >> "team_distribution\HOW_TO_USE.txt"

REM Get file size
for %%A in ("team_distribution\PACE.exe") do set "FILE_SIZE=%%~zA"
set /a "SIZE_MB=%FILE_SIZE% / 1048576"

echo =====================================
echo SUCCESS! Ready for Distribution
echo =====================================
echo.
echo Your team distribution is ready:
echo.
echo 📁 team_distribution\
echo    ├── PACE.exe (%SIZE_MB% MB)
echo    └── HOW_TO_USE.txt
echo.
echo DISTRIBUTION OPTIONS:
echo =====================================
echo.
echo Option 1: Single File (Easiest)
echo   → Send just PACE.exe to your team
echo   → They double-click and it works immediately
echo   → No installation needed
echo.
echo Option 2: Complete Package
echo   → Zip the team_distribution folder
echo   → Send the zip file to your team
echo   → Includes instructions
echo.
echo Option 3: Network Share
echo   → Copy team_distribution to a shared network folder
echo   → Team members can run PACE.exe directly from the network
echo.
echo NEXT STEPS:
echo =====================================
echo 1. Test PACE.exe yourself first
echo 2. Choose your distribution method above
echo 3. Send to your team with simple instructions:
echo    "Download PACE.exe and double-click to run"
echo.

REM Test the executable quickly
echo Testing the executable...
start /min "PACE Test" "team_distribution\PACE.exe"
timeout /t 3 >nul
taskkill /F /IM PACE.exe >nul 2>&1
echo ✓ Executable starts successfully
echo.

echo Opening team distribution folder...
explorer "team_distribution"

echo.
echo =====================================
echo DISTRIBUTION COMPLETE!
echo =====================================
echo.
echo Your team can now use PACE without any technical setup!
echo.
pause
