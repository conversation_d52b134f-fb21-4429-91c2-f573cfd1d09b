#!/usr/bin/env python3
"""
Fix font consistency issues in Greenberg Traurig MSA documents.
"""

import os
from docx import Document

def fix_greenberg_template_fonts(docx_path):
    """
    Fix font consistency in Greenberg Traurig templates.
    
    Args:
        docx_path (str): Path to the generated document
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        print(f"Fixing fonts in Greenberg template: {docx_path}")
        
        # Load the document
        doc = Document(docx_path)
        
        # Define the target font (what Greenberg templates should use)
        target_font = "Times New Roman"  # Most legal documents use Times New Roman
        
        # Fix fonts in all paragraphs
        for paragraph in doc.paragraphs:
            for run in paragraph.runs:
                if run.font.name != target_font:
                    print(f"Changing font from '{run.font.name}' to '{target_font}' in text: '{run.text[:50]}...'")
                    run.font.name = target_font
        
        # Fix fonts in all tables
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    for paragraph in cell.paragraphs:
                        for run in paragraph.runs:
                            if run.font.name != target_font:
                                print(f"Changing table font from '{run.font.name}' to '{target_font}' in text: '{run.text[:50]}...'")
                                run.font.name = target_font
        
        # Save the document
        doc.save(docx_path)
        print(f"✓ Font consistency fixed in {docx_path}")
        return True
        
    except Exception as e:
        print(f"✗ Error fixing fonts in {docx_path}: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_greenberg_font_fix():
    """Test the font fixing on a sample Greenberg document"""
    print("Testing Greenberg font fix...")
    
    try:
        from models.dfir import DFIREngagement
        from models.client import Client
        from utils.document_generator import DocumentGenerator
        import tempfile
        
        # Create test client with Greenberg Traurig
        client = Client()
        client.name = "Test Client Corp"
        client.address = "123 Test Street, Test City, TS 12345"
        client.law_firm = "Greenberg Traurig, LLP"
        client.insurance_carrier = "Test Insurance"
        
        # Create engagement
        engagement = DFIREngagement()
        engagement.client = client
        engagement.contact_name = "John Doe"
        engagement.contact_email = "<EMAIL>"
        engagement.contact_phone = "************"
        engagement.engagement_type = "IR Investigation"
        
        print(f"✓ Created test engagement for {client.law_firm}")
        
        # Generate MSA document
        with tempfile.TemporaryDirectory() as temp_dir:
            doc_gen = DocumentGenerator(engagement, temp_dir)
            
            # Generate MSA
            msa_path = doc_gen.generate_msa()
            if msa_path and os.path.exists(msa_path):
                print(f"✓ Generated MSA: {msa_path}")
                
                # Apply font fix
                if fix_greenberg_template_fonts(msa_path):
                    print("✓ Font fix applied successfully")
                    
                    # Copy to current directory for inspection
                    import shutil
                    output_path = "test_greenberg_msa_fixed.docx"
                    shutil.copy2(msa_path, output_path)
                    print(f"✓ Fixed document saved as: {output_path}")
                    return True
                else:
                    print("✗ Font fix failed")
                    return False
            else:
                print("✗ Failed to generate MSA")
                return False
                
    except Exception as e:
        print(f"✗ Error testing font fix: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function"""
    print("Greenberg Traurig Font Fix Tool")
    print("=" * 40)
    
    # Test the font fix
    if test_greenberg_font_fix():
        print("\n✓ Font fix test completed successfully!")
        print("Check the generated test_greenberg_msa_fixed.docx file")
    else:
        print("\n✗ Font fix test failed")
        
    print("\nTo apply this fix to the document generator:")
    print("1. The fix function is ready to integrate")
    print("2. It should be called after document generation")
    print("3. Only for Greenberg Traurig templates")

if __name__ == "__main__":
    main()
