#!/usr/bin/env python3
"""
Quick test to verify the main fixes.
"""

import os

def main():
    print("PACE Quick Fix Verification")
    print("=" * 30)
    
    # Check executable exists
    if os.path.exists("dist/PACE.exe"):
        size = os.path.getsize("dist/PACE.exe") / (1024*1024)
        print(f"✓ PACE.exe exists ({size:.1f} MB)")
    else:
        print("✗ PACE.exe not found")
        return
    
    # Check icon
    if os.path.exists("PACE.ico"):
        icon_size = os.path.getsize("PACE.ico")
        if icon_size > 50000:
            print(f"✓ Original icon restored ({icon_size:,} bytes)")
        else:
            print(f"⚠ Icon may not be original ({icon_size:,} bytes)")
    else:
        print("✗ PACE.ico not found")
    
    # Test legal_date
    try:
        from models.dfir import DFIREngagement
        engagement = DFIREngagement()
        legal_date = engagement._get_legal_date()
        print(f"✓ legal_date working: {legal_date}")
    except:
        print("✗ legal_date function failed")
    
    print("\n" + "=" * 30)
    print("MANUAL TESTS NEEDED:")
    print("1. Run dist\\PACE.exe")
    print("2. Check for gear icon in taskbar")
    print("3. Generate a document")
    print("4. Check formatting")

if __name__ == "__main__":
    main()
