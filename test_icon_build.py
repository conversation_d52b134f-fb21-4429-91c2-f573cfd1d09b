#!/usr/bin/env python3
"""
Quick test build to verify icon embedding.
"""

import os
import sys
import subprocess
from pathlib import Path

def test_icon_build():
    """Test building with icon"""
    print("Testing icon embedding in executable...")
    
    # Check if icon exists
    if not os.path.exists('PACE.ico'):
        print("✗ PACE.ico not found")
        return False
    
    print("✓ PACE.ico found")
    
    # Get icon file size
    icon_size = os.path.getsize('PACE.ico')
    print(f"✓ Icon size: {icon_size} bytes")
    
    # Create a minimal spec file for testing
    test_spec = '''# -*- mode: python ; coding: utf-8 -*-

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('templates', 'templates'),
        ('data', 'data'),
        ('resources', 'resources'),
    ],
    hiddenimports=[
        'PySide6.QtCore',
        'PySide6.QtWidgets', 
        'PySide6.QtGui',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='PACE_test',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='PACE.ico'
)
'''
    
    # Write test spec
    with open('PACE_test.spec', 'w') as f:
        f.write(test_spec)
    
    print("✓ Created test spec file")
    
    try:
        # Build test executable
        print("Building test executable...")
        cmd = [sys.executable, "-m", "PyInstaller", "--clean", "PACE_test.spec"]
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            print("✓ Test build successful")
            
            # Check if executable was created
            test_exe = Path("dist/PACE_test.exe")
            if test_exe.exists():
                size_mb = test_exe.stat().st_size / (1024 * 1024)
                print(f"✓ Test executable created: {size_mb:.1f} MB")
                
                # Try to extract icon info (basic check)
                print("✓ Icon should be embedded in executable")
                return True
            else:
                print("✗ Test executable not found")
                return False
        else:
            print("✗ Test build failed:")
            print(result.stderr[:500])  # Show first 500 chars of error
            return False
            
    except subprocess.TimeoutExpired:
        print("✗ Build timed out")
        return False
    except Exception as e:
        print(f"✗ Build error: {e}")
        return False
    finally:
        # Clean up test files
        try:
            if os.path.exists('PACE_test.spec'):
                os.remove('PACE_test.spec')
        except:
            pass

def main():
    """Main test function"""
    print("PACE Icon Test Build")
    print("=" * 30)
    
    success = test_icon_build()
    
    print("\n" + "=" * 30)
    if success:
        print("✓ ICON TEST PASSED")
        print("The icon should be properly embedded in the executable")
    else:
        print("✗ ICON TEST FAILED")
        print("Check the icon file and build configuration")
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
