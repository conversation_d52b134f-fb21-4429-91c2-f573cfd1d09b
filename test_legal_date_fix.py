#!/usr/bin/env python3
"""
Simple test for legal_date fix.
"""

def test_legal_date():
    print("Testing legal_date fix...")
    
    try:
        from models.dfir import DFIREngagement
        from models.client import Client
        
        # Create test client
        client = Client()
        client.name = "Test Client"
        client.law_firm = "<PERSON>, LLP"
        
        # Create engagement
        engagement = DFIREngagement()
        engagement.client = client
        
        # Get placeholders
        placeholders = engagement.get_placeholders()
        
        # Check for legal_date
        if 'legal_date' in placeholders:
            print(f"✓ legal_date found: {placeholders['legal_date']}")
            return True
        else:
            print("✗ legal_date not found")
            return False
            
    except Exception as e:
        print(f"✗ Error: {e}")
        return False

if __name__ == "__main__":
    success = test_legal_date()
    print(f"Test {'PASSED' if success else 'FAILED'}")
