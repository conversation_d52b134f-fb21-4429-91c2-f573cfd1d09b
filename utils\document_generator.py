"""
Document generator for PACE application.
"""

import os
import sys
import re
import logging
import shutil
import tempfile
from datetime import datetime

# Import document generation dependencies at module level for performance
try:
    from docxtpl import DocxTemplate, RichText
    import jinja2
    from utils.docx_wrapper import generate_document_with_docxtpl
    DOCXTPL_AVAILABLE = True
except ImportError:
    DOCXTPL_AVAILABLE = False
    DocxTemplate = None
    RichText = None
    jinja2 = None
    generate_document_with_docxtpl = None

# Set up detailed logging to user's temp directory to avoid permission issues
import tempfile
log_file = os.path.join(tempfile.gettempdir(), "pace_document_generation.log")
logging.basicConfig(level=logging.INFO,  # Changed from DEBUG to INFO for cleaner output
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                    handlers=[
                        logging.FileHandler(log_file),
                        logging.StreamHandler()
                    ])
logger = logging.getLogger(__name__)

def debug_document(path, prefix="DEBUG"):
    """Debug a document by checking its existence, size, and content."""
    try:
        if not os.path.exists(path):
            logger.error(f"{prefix} - Document does not exist: {path}")
            return False

        size = os.path.getsize(path)
        logger.info(f"{prefix} - Document exists with size: {size} bytes at {path}")

        # Check if it's a valid Office document (starts with PK)
        with open(path, 'rb') as f:
            header = f.read(4)
            if header.startswith(b'PK'):
                logger.info(f"{prefix} - Document has valid Office format header")
                return True
            else:
                logger.error(f"{prefix} - Document does not have valid Office format header: {header}")
                return False
    except Exception as e:
        logger.error(f"{prefix} - Error debugging document: {str(e)}")
        return False

# Removed simple_generate_document function - no longer needed

# Define custom exception for document generation errors
class DocumentGenerationError(Exception):
    """Exception raised for errors in document generation."""
    pass

def _fix_greenberg_template_fonts(docx_path):
    """
    Fix font consistency and formatting in Greenberg Traurig templates.

    Args:
        docx_path (str): Path to the generated document

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        from docx import Document
        from docx.shared import Pt

        logger.info(f"Fixing fonts and formatting in Greenberg template: {docx_path}")

        # Load the document
        doc = Document(docx_path)

        # Define the target font (what Greenberg templates should use)
        target_font = "Garamond"  # Greenberg Traurig uses Garamond
        target_size = Pt(12)  # Standard legal document size

        # Fix fonts and formatting in all paragraphs
        for paragraph in doc.paragraphs:
            for run in paragraph.runs:
                # Fix font name
                if run.font.name != target_font:
                    run.font.name = target_font

                # Fix font size if it's not set or inconsistent
                if run.font.size is None or run.font.size != target_size:
                    run.font.size = target_size

                # Explicitly ensure no italic formatting is applied
                if run.font.italic is True:
                    run.font.italic = False

                # Clean up any extra spaces or formatting issues
                if run.text:
                    # Fix common spacing issues
                    cleaned_text = run.text
                    # Remove extra spaces around periods
                    cleaned_text = cleaned_text.replace(' .', '.')
                    # Fix spacing after periods
                    cleaned_text = cleaned_text.replace('.  ', '. ')
                    # Remove multiple consecutive spaces
                    import re
                    cleaned_text = re.sub(r' +', ' ', cleaned_text)
                    run.text = cleaned_text

        # Fix fonts and formatting in all tables
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    for paragraph in cell.paragraphs:
                        for run in paragraph.runs:
                            # Fix font name
                            if run.font.name != target_font:
                                run.font.name = target_font

                            # Fix font size
                            if run.font.size is None or run.font.size != target_size:
                                run.font.size = target_size

                            # Explicitly ensure no italic formatting is applied
                            if run.font.italic is True:
                                run.font.italic = False

        # Save the document
        doc.save(docx_path)
        logger.info(f"Font consistency and formatting fixed in Greenberg template: {docx_path}")
        return True

    except Exception as e:
        logger.error(f"Error fixing fonts in Greenberg template {docx_path}: {e}")
        return False

# Centralized document generation function to eliminate duplication
def _generate_document_with_docxtpl(template_path, output_path, placeholders):
    """
    Centralized function to generate documents using DocxTemplate.
    This eliminates code duplication across multiple methods.

    Args:
        template_path (str): Path to the template file
        output_path (str): Path to save the generated document
        placeholders (dict): Placeholders to replace in the template

    Returns:
        str: Path to the generated document, or None if generation failed
    """
    if not DOCXTPL_AVAILABLE:
        logger.error("DocxTemplate not available")
        return None

    try:
        # Create the output directory if it doesn't exist
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        # Load the template
        doc = DocxTemplate(template_path)

        # Create a custom Jinja2 environment to handle ampersands correctly
        custom_jinja_env = jinja2.Environment(autoescape=False)
        doc.jinja_env = custom_jinja_env

        # Check if this is a Greenberg Traurig template (needs special handling)
        is_greenberg_template = 'Greenberg' in template_path

        # Process placeholders to ensure pricing values are plain text (no bold formatting)
        processed_placeholders = {}
        pricing_keys = [
            'phase1_sp_hours', 'phase2_sp_hours', 'phase3_sp_hours', 'phase4_sp_hours', 'phase5_sp_hours', 'phase6_sp_hours',
            'phase1_sp_costs', 'phase2_sp_costs', 'phase3_sp_costs', 'phase4_sp_costs', 'phase5_sp_costs', 'phase6_sp_costs',
            'phase1_hours', 'phase2_hours', 'phase3_hours', 'phase4_hours', 'phase5_hours', 'phase6_hours',
            'phase1_costs', 'phase2_costs', 'phase3_costs', 'phase4_costs', 'phase5_costs', 'phase6_costs',
            'cs120_forensic_hours', 'cs120_forensic_costs', 'cs120_email_hours', 'cs120_email_costs',
            'cs210_hours', 'cs210_costs', 'cs410_hours', 'cs410_costs', 'cs430_hours', 'cs430_costs',
            'total_hours', 'total_costs', 'sp_total_hours', 'sp_costs', 'sp_total_cost',
            'dfir_rate', 'hourly_rate', 'edr_fee', 'mandatory_fee',
            'bec_rate', 'bec_total_cost', 'bec_hours', 'bec_costs'
        ]

        for key, value in placeholders.items():
            if is_greenberg_template:
                # For Greenberg templates, convert ALL placeholders to plain text to preserve formatting
                if hasattr(value, 'text'):  # RichText object
                    processed_placeholders[key] = str(value.text)
                elif hasattr(value, 'xml'):  # RichText object with xml attribute
                    processed_placeholders[key] = str(value)
                else:
                    processed_placeholders[key] = str(value)
            elif key in pricing_keys:
                # For pricing-related fields, ensure they're plain text without formatting
                if hasattr(value, 'text'):  # RichText object
                    processed_placeholders[key] = str(value.text)
                else:
                    processed_placeholders[key] = str(value)
            else:
                # For other fields, preserve original formatting
                processed_placeholders[key] = value

        # Replace placeholders
        doc.render(processed_placeholders)

        # Save the document
        doc.save(output_path)

        # Post-process Greenberg templates to fix font consistency
        if is_greenberg_template:
            _fix_greenberg_template_fonts(output_path)

        logger.info(f"Generated document from {template_path} to {output_path} using DocxTemplate")
        return output_path

    except Exception as e:
        logger.error(f"Error generating document with DocxTemplate: {e}")
        return None

# Define helper functions for document generation
def validate_template(template_path):
    """Validate that a template file exists and is a valid Office document."""
    if not os.path.exists(template_path):
        print(f"Template file does not exist: {template_path}")
        return False

    try:
        with open(template_path, 'rb') as f:
            # Check for Office Open XML signature (PK)
            signature = f.read(4)
            if signature.startswith(b'PK'):
                return True
            else:
                print(f"Template file is not a valid Office document: {template_path}")
                return False
    except Exception as e:
        print(f"Error validating template: {str(e)}")
        return False

def save_document_safely(doc, output_path):
    """Save a document safely, ensuring it's properly closed."""
    try:
        # Create the output directory if it doesn't exist
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        # Save the document
        doc.save(output_path)

        # Verify the file exists
        if os.path.exists(output_path):
            return True
        else:
            print(f"Document was not saved: {output_path}")
            return False
    except Exception as e:
        print(f"Error saving document: {str(e)}")
        return False

def verify_document_structure(output_path):
    """Verify that a document has a valid structure."""
    try:
        with open(output_path, 'rb') as f:
            # Check for Office Open XML signature (PK)
            signature = f.read(4)
            if signature.startswith(b'PK'):
                return True
            else:
                print(f"Document has invalid structure: {output_path}")
                return False
    except Exception as e:
        print(f"Error verifying document structure: {str(e)}")
        return False

def recover_from_failed_generation(output_path, template_backup):
    """Recover from a failed document generation by copying the template backup."""
    try:
        # Create the output directory if it doesn't exist
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        # Copy the template backup to the output path
        shutil.copy2(template_backup, output_path)

        # Verify the file exists
        if os.path.exists(output_path):
            return True
        else:
            print(f"Failed to recover document: {output_path}")
            return False
    except Exception as e:
        print(f"Error recovering from failed generation: {str(e)}")
        return False

# Removed unused Document import

# Determine if we're running in a PyInstaller bundle
def is_bundled():
    return getattr(sys, 'frozen', False) and hasattr(sys, '_MEIPASS')

# Get the base directory (works in both development and PyInstaller environments)
def get_base_dir():
    if is_bundled():
        return sys._MEIPASS
    else:
        return os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# Add the parent directory to the path for imports
sys.path.append(get_base_dir())

# Define a document generation function that replaces placeholders
def clean_xml_tags(docx_path):
    """
    Clean XML tags from a Word document.
    This function opens a Word document, removes XML tags, and saves it back.

    Args:
        docx_path (str): Path to the Word document to clean

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        from docx import Document
        import re

        # Load the document
        doc = Document(docx_path)

        # Regular expressions to match XML tags
        xml_pattern = re.compile(r'<w:r><w:t xml:space="preserve">(.*?)</w:t></w:r>')
        nested_xml_pattern = re.compile(r'&lt;w:r&gt;&lt;w:t xml:space=&quot;preserve&quot;&gt;(.*?)&lt;/w:t&gt;&lt;/w:r&gt;')
        amp_pattern = re.compile(r'&amp;amp;')

        # Process each paragraph
        for para in doc.paragraphs:
            # Check if the paragraph contains XML tags
            if '<w:r>' in para.text or '&lt;w:r&gt;' in para.text:
                # First, handle nested XML tags
                cleaned_text = nested_xml_pattern.sub(r'\1', para.text)
                # Then handle direct XML tags
                cleaned_text = xml_pattern.sub(r'\1', cleaned_text)
                # Fix double-encoded ampersands
                cleaned_text = amp_pattern.sub('&', cleaned_text)
                # Replace the paragraph text
                para.text = cleaned_text

        # Process each table
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    for para in cell.paragraphs:
                        # Check if the paragraph contains XML tags
                        if '<w:r>' in para.text or '&lt;w:r&gt;' in para.text:
                            # First, handle nested XML tags
                            cleaned_text = nested_xml_pattern.sub(r'\1', para.text)
                            # Then handle direct XML tags
                            cleaned_text = xml_pattern.sub(r'\1', cleaned_text)
                            # Fix double-encoded ampersands
                            cleaned_text = amp_pattern.sub('&', cleaned_text)
                            # Replace the paragraph text
                            para.text = cleaned_text

        # Save the document
        doc.save(docx_path)
        print(f"Cleaned XML tags from {docx_path}")
        return True
    except Exception as e:
        print(f"Error cleaning XML tags from {docx_path}: {str(e)}")
        return False

def generate_document(template_path, output_path, placeholders):
    """Generate a document by replacing placeholders in the template."""
    if not DOCXTPL_AVAILABLE:
        logger.error("DocxTemplate not available, falling back to simple copy")
        try:
            shutil.copy2(template_path, output_path)
            return output_path
        except Exception as e:
            logger.error(f"Failed to copy template: {e}")
            return None

    # Validate the template before processing
    if not validate_template(template_path):
        logger.error(f"Invalid template: {template_path}")
        raise DocumentGenerationError(f"Invalid template: {template_path}")

    # Use the centralized document generation function
    result = _generate_document_with_docxtpl(template_path, output_path, placeholders)
    if result:
        return result

    # Fallback: try simple copy
    try:
        logger.info("DocxTemplate generation failed, falling back to simple copy")
        shutil.copy2(template_path, output_path)
        return output_path
    except Exception as e:
        logger.error(f"Fallback copy failed: {e}")
        return None
    except ImportError as e:
        print(f"DocxTemplate not available: {e}, trying to install it")
        try:
            import subprocess
            subprocess.check_call([sys.executable, "-m", "pip", "install", "docxtpl"])
            print("Successfully installed docxtpl, trying again")

            # Try again with docxtpl
            from docxtpl import DocxTemplate
            import jinja2
            doc = DocxTemplate(template_path)
            # Create a custom Jinja2 environment to handle ampersands correctly
            custom_jinja_env = jinja2.Environment(autoescape=False)
            doc.jinja_env = custom_jinja_env
            doc.render(placeholders)
            doc.save(output_path)
            print(f"Generated document from {template_path} to {output_path} after installing docxtpl")
            return output_path
        except Exception as install_error:
            print(f"Failed to install docxtpl: {install_error}, falling back to simple copy")
            import shutil
            shutil.copy2(template_path, output_path)
            print(f"Copied template {template_path} to {output_path}")
            return output_path
    except Exception as e:
        print(f"Error generating document: {str(e)}")
        import traceback
        traceback.print_exc()

        # Create a backup of the template for safety
        template_backup = template_path + ".bak"
        if not os.path.exists(template_backup):
            try:
                shutil.copy2(template_path, template_backup)
                print(f"Created backup of template at {template_backup}")
            except Exception as backup_error:
                print(f"Error creating template backup: {backup_error}")

        # Try a simple copy as a fallback
        try:
            # If the template exists but can't be rendered, create a copy
            if os.path.exists(template_path):
                # Create the output directory if it doesn't exist
                os.makedirs(os.path.dirname(output_path), exist_ok=True)

                # Copy the template to the output path
                shutil.copy2(template_path, output_path)
                print(f"Created a copy of the template at {output_path} as a fallback")

                # Verify the document was created successfully
                if os.path.exists(output_path):
                    # Try to open the document to verify it's valid
                    try:
                        with open(output_path, 'rb') as f:
                            content = f.read(4)
                            if content.startswith(b'PK'):
                                print(f"Verified document structure is valid: {output_path}")
                                return output_path
                            else:
                                print(f"Warning: Document may have invalid structure: {output_path}")
                                return output_path
                    except Exception as verify_error:
                        print(f"Error verifying document: {verify_error}")
                        return output_path
                else:
                    print(f"Error: Failed to create document at {output_path}")
        except Exception as copy_error:
            print(f"Error creating fallback copy: {copy_error}")

        return None

class DocumentGenerator:
    """
    Class for generating documents for engagements.
    """

    def __init__(self, engagement, output_dir=None):
        """
        Initialize a new DocumentGenerator object.

        Args:
            engagement: The engagement object
            output_dir (str): The directory where documents should be saved
        """
        self.engagement = engagement

        # Store the base directory for template resolution
        self.base_dir = get_base_dir()
        print(f"Base directory: {self.base_dir}")

        # Set the output directory
        if output_dir:
            self.output_dir = output_dir
        else:
            self.output_dir = os.path.join(os.getcwd(), self.engagement.get_document_folder_name())

        # Create the output directory if it doesn't exist
        os.makedirs(self.output_dir, exist_ok=True)

        # Print debug information about the environment
        print(f"Running in bundled mode: {is_bundled()}")
        print(f"Current working directory: {os.getcwd()}")
        print(f"Output directory: {self.output_dir}")

    def resolve_template_path(self, template_path):
        """
        Resolve the template path to work in both development and compiled environments.

        Args:
            template_path (str): The template path to resolve

        Returns:
            str: The resolved template path
        """
        # If we're running in a bundled environment, use the base directory
        if is_bundled():
            # Remove any leading path separators
            if template_path.startswith('/'):
                template_path = template_path[1:]
            elif template_path.startswith('\\'):
                template_path = template_path[2:]

            # Join with the base directory
            resolved_path = os.path.join(self.base_dir, template_path)
            print(f"Resolved template path: {resolved_path}")
            return resolved_path
        else:
            # In development mode, use the path as is
            return template_path


    # Removed direct document generation methods that were creating simplified documents
    def generate_documents(self):
        """
        Generate all documents for the engagement.

        Returns:
            list: List of paths to the generated documents
        """
        print(f"Generating documents for {self.engagement.engagement_type} engagement")
        print(f"Client: {self.engagement.client.name}")
        print(f"Law Firm: {self.engagement.client.law_firm}")
        print(f"Output directory: {self.output_dir}")

        generated_documents = []

        # Generate SOW
        sow_path = self.generate_sow()
        if sow_path:
            generated_documents.append(sow_path)

        # Check if TACI is needed
        if hasattr(self.engagement, 'include_taci') and self.engagement.include_taci:
            print("Checking for TACI: hasattr=True, value=True")
            taci_path = self.generate_taci_sow()
            if taci_path:
                generated_documents.append(taci_path)
        else:
            print(f"Checking for TACI: hasattr={hasattr(self.engagement, 'include_taci')}, value={getattr(self.engagement, 'include_taci', False)}")

        # Check if RR SOW document should be generated
        should_generate_rr = (
            self.engagement.engagement_type == "RR" or
            (hasattr(self.engagement, 'generate_rr_sow') and self.engagement.generate_rr_sow)
        )

        if should_generate_rr:
            print(f"Generating RR SOW: engagement_type={self.engagement.engagement_type} or generate_rr_sow={getattr(self.engagement, 'generate_rr_sow', False)}")
            rr_path = self.generate_rr_sow()
            if rr_path:
                generated_documents.append(rr_path)
        else:
            print(f"Skipping RR SOW: engagement_type={self.engagement.engagement_type}, generate_rr_sow={getattr(self.engagement, 'generate_rr_sow', False)}")

        # Generate MSA
        msa_path = self.generate_msa()
        if msa_path:
            generated_documents.append(msa_path)

        # Generate BAA
        print(f"DEBUG - Client object: {self.engagement.client}")
        print(f"DEBUG - Client attributes: {dir(self.engagement.client)}")
        print(f"DEBUG - Client needs_baa attribute exists: {hasattr(self.engagement.client, 'needs_baa')}")
        print(f"DEBUG - Client needs_baa value: {getattr(self.engagement.client, 'needs_baa', 'NOT SET')}")

        if hasattr(self.engagement.client, 'needs_baa') and self.engagement.client.needs_baa:
            print(f"Client needs BAA: {self.engagement.client.needs_baa}")
            baa_path = self.generate_baa()
            print(f"DEBUG - BAA path returned from generate_baa: {baa_path}")
            if baa_path:
                generated_documents.append(baa_path)
                print(f"Added BAA to generated documents: {baa_path}")
                # Verify the BAA document exists
                if os.path.exists(baa_path):
                    print(f"DEBUG - BAA document exists at: {baa_path}")
                    # Check if the file is readable
                    try:
                        with open(baa_path, 'rb') as f:
                            f.read(1)  # Try to read 1 byte
                        print(f"DEBUG - BAA document is readable")
                    except Exception as e:
                        print(f"DEBUG - BAA document is not readable: {e}")
                else:
                    print(f"DEBUG - BAA document does not exist at: {baa_path}")
            else:
                print("Failed to generate BAA document")
        else:
            print(f"Client does not need BAA: hasattr={hasattr(self.engagement.client, 'needs_baa')}, value={getattr(self.engagement.client, 'needs_baa', False)}")

        # Generate DPA
        print(f"DEBUG - Client needs_dpa attribute exists: {hasattr(self.engagement.client, 'needs_dpa')}")
        print(f"DEBUG - Client needs_dpa value: {getattr(self.engagement.client, 'needs_dpa', 'NOT SET')}")

        if hasattr(self.engagement.client, 'needs_dpa') and self.engagement.client.needs_dpa:
            print(f"Client needs DPA: {self.engagement.client.needs_dpa}")
            dpa_path = self.generate_dpa()
            print(f"DEBUG - DPA path returned from generate_dpa: {dpa_path}")
            if dpa_path:
                generated_documents.append(dpa_path)
                print(f"Added DPA to generated documents: {dpa_path}")
                # Verify the DPA document exists
                if os.path.exists(dpa_path):
                    print(f"DEBUG - DPA document exists at: {dpa_path}")
                    # Check if the file is readable
                    try:
                        with open(dpa_path, 'rb') as f:
                            f.read(1)  # Try to read 1 byte
                        print(f"DEBUG - DPA document is readable")
                    except Exception as e:
                        print(f"DEBUG - DPA document is not readable: {e}")
                else:
                    print(f"DEBUG - DPA document does not exist at: {dpa_path}")
            else:
                print("Failed to generate DPA document")
        else:
            print(f"Client does not need DPA: hasattr={hasattr(self.engagement.client, 'needs_dpa')}, value={getattr(self.engagement.client, 'needs_dpa', False)}")

        # Debug output for final document list
        print(f"DEBUG - DocumentGenerator - Final document list count: {len(generated_documents)}")
        for i, doc in enumerate(generated_documents):
            print(f"DEBUG - DocumentGenerator - Document {i+1}: {os.path.basename(doc)}")

        # Verify that all documents exist in the output directory
        print(f"DEBUG - DocumentGenerator - Verifying all documents exist in output directory: {self.output_dir}")
        for i, doc in enumerate(generated_documents):
            if os.path.exists(doc):
                print(f"DEBUG - DocumentGenerator - Document {i+1} exists: {os.path.basename(doc)}")
            else:
                print(f"DEBUG - DocumentGenerator - Document {i+1} DOES NOT EXIST: {os.path.basename(doc)}")
                # Try to recreate the document if it doesn't exist
                try:
                    if "IR Investigation" in doc:
                        print(f"DEBUG - DocumentGenerator - Attempting to recreate SOW document")
                        sow_path = self.generate_sow()
                        if sow_path and sow_path != doc:
                            generated_documents[i] = sow_path
                    elif "Ransom Communications" in doc:
                        print(f"DEBUG - DocumentGenerator - Attempting to recreate TACI SOW document")
                        taci_path = self.generate_taci_sow()
                        if taci_path and taci_path != doc:
                            generated_documents[i] = taci_path
                    elif "Recovery & Remediation" in doc:
                        print(f"DEBUG - DocumentGenerator - Attempting to recreate RR SOW document")
                        rr_path = self.generate_rr_sow()
                        if rr_path and rr_path != doc:
                            generated_documents[i] = rr_path
                    elif "Master Services Agreement" in doc:
                        print(f"DEBUG - DocumentGenerator - Attempting to recreate MSA document")
                        msa_path = self.generate_msa()
                        if msa_path and msa_path != doc:
                            generated_documents[i] = msa_path
                    elif "Business Associate Agreement" in doc:
                        print(f"DEBUG - DocumentGenerator - Attempting to recreate BAA document")
                        baa_path = self.generate_baa()
                        if baa_path and baa_path != doc:
                            generated_documents[i] = baa_path
                    elif "Data Processing Agreement" in doc:
                        print(f"DEBUG - DocumentGenerator - Attempting to recreate DPA document")
                        dpa_path = self.generate_dpa()
                        if dpa_path and dpa_path != doc:
                            generated_documents[i] = dpa_path
                except Exception as e:
                    print(f"DEBUG - DocumentGenerator - Error recreating document: {e}")

        return generated_documents

    def generate_sow(self):
        """
        Generate the SOW document.

        Returns:
            str: Path to the generated SOW document
        """
        print("Generating SOW...")

        # Get the template path
        template_path = self.engagement.get_sow_template_path()
        print(f"SOW template path: {template_path}")

        # Resolve the template path for bundled environment
        template_path = self.resolve_template_path(template_path)
        print(f"Resolved SOW template path: {template_path}")

        # Check if the template exists
        if not os.path.exists(template_path):
            print(f"SOW template not found: {template_path}")
            return None

        # Get the output path
        output_path = os.path.join(self.output_dir, self.engagement.get_sow_filename())
        print(f"SOW output path: {output_path}")

        # Get the placeholders
        placeholders = self._get_placeholders()

        # Generate the document
        try:
            # Create the output directory if it doesn't exist
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            # Use centralized document generation function
            result = _generate_document_with_docxtpl(template_path, output_path, placeholders)
            if not result:
                # Fall back to the simple document generator
                print("DocxTemplate generation failed, falling back to simple document generator")
                result = generate_document(template_path, output_path, placeholders)

            # Verify the document was created
            if os.path.exists(output_path):
                print(f"SOW document generated successfully: {output_path}")
                return output_path
            else:
                print(f"SOW document generation failed: Output file not found at {output_path}")
                return None
        except Exception as e:
            print(f"Error generating SOW document: {str(e)}")
            import traceback
            traceback.print_exc()

            # For debugging, create a simple text file with the placeholders
            debug_path = output_path.replace('.docx', '_debug.txt')
            try:
                with open(debug_path, 'w') as f:
                    f.write(f"Error generating SOW: {str(e)}\n\n")
                    f.write(f"Template path: {template_path}\n")
                    f.write(f"Output path: {output_path}\n\n")
                    f.write("Placeholders:\n")
                    for key, value in placeholders.items():
                        f.write(f"{key}: {value}\n")
                print(f"Debug info written to: {debug_path}")
            except Exception as debug_error:
                print(f"Error writing debug info: {str(debug_error)}")
            return None

    # Removed unused modular document generation methods

    def generate_msa(self):
        """
        Generate the MSA document.

        Returns:
            str: Path to the generated MSA document
        """
        print("Generating MSA...")

        # Get the template path
        template_path = self.engagement.get_msa_template_path()
        print(f"MSA template path: {template_path}")

        # Resolve the template path for bundled environment
        template_path = self.resolve_template_path(template_path)
        print(f"Resolved MSA template path: {template_path}")

        # Check if the template exists
        if not os.path.exists(template_path):
            print(f"MSA template not found: {template_path}")
            return None

        # Get the output path
        output_path = os.path.join(self.output_dir, self.engagement.get_msa_filename())
        print(f"MSA output path: {output_path}")

        # Get the placeholders
        placeholders = self._get_placeholders()

        # Generate the document
        try:
            # Create the output directory if it doesn't exist
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            # Use centralized document generation function
            result = _generate_document_with_docxtpl(template_path, output_path, placeholders)
            if not result:
                # Fall back to the simple document generator
                print("DocxTemplate generation failed, falling back to simple document generator")
                result = generate_document(template_path, output_path, placeholders)

            # Verify the document was created
            if os.path.exists(output_path):
                print(f"MSA document generated successfully: {output_path}")
                return output_path
            else:
                print(f"MSA document generation failed: Output file not found at {output_path}")
                return None
        except Exception as e:
            print(f"Error generating MSA document: {str(e)}")
            import traceback
            traceback.print_exc()

            # For debugging, create a simple text file with the placeholders
            debug_path = output_path.replace('.docx', '_debug.txt')
            try:
                with open(debug_path, 'w') as f:
                    f.write(f"Error generating MSA: {str(e)}\n\n")
                    f.write(f"Template path: {template_path}\n")
                    f.write(f"Output path: {output_path}\n\n")
                    f.write("Placeholders:\n")
                    for key, value in placeholders.items():
                        f.write(f"{key}: {value}\n")
                print(f"Debug info written to: {debug_path}")
            except Exception as debug_error:
                print(f"Error writing debug info: {str(debug_error)}")
            return None

    def generate_baa(self):
        """
        Generate the BAA document.

        Returns:
            str: Path to the generated BAA document
        """
        print("Generating BAA...")

        # Hardcode the template path that we know works
        template_path = "templates/base_templates/baa/Template_Business_Associate_Agreement (BAA).docx"
        print(f"BAA template path (hardcoded): {template_path}")

        # Check if the template exists
        if not os.path.exists(template_path):
            print(f"BAA template not found at hardcoded path: {template_path}")
            # Try with the path from the engagement model as a fallback
            template_path = self.engagement.get_baa_template_path()
            print(f"Trying BAA template path from model: {template_path}")

            # Resolve the template path for bundled environment
            template_path = self.resolve_template_path(template_path)
            print(f"Resolved BAA template path: {template_path}")

            # Check if the template exists
            if not os.path.exists(template_path):
                print(f"BAA template not found at resolved path: {template_path}")
                return None

        # Get the output path
        output_path = os.path.join(self.output_dir, self.engagement.get_baa_filename())
        print(f"BAA output path: {output_path}")

        # Get the placeholders
        placeholders = self._get_placeholders()

        # Generate the document directly using DocxTemplate
        try:
            # Create the output directory if it doesn't exist
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            # Use centralized document generation function
            result = _generate_document_with_docxtpl(template_path, output_path, placeholders)
            if not result:
                print("DocxTemplate generation failed")
                return None

            print(f"BAA document generated successfully: {output_path}")

            # Verify the document was created
            if os.path.exists(output_path):
                # Debug the document
                debug_document(output_path, "BAA")
                return output_path
            else:
                print(f"BAA document generation failed: Output file not found at {output_path}")
                return None
        except Exception as e:
            print(f"Error generating BAA document: {str(e)}")
            import traceback
            traceback.print_exc()

            # As a last resort, try to copy the template to the output path
            try:
                shutil.copy2(template_path, output_path)
                print(f"Created a copy of the BAA template at {output_path} as a fallback")
                return output_path
            except Exception as copy_error:
                print(f"Error copying BAA template: {str(copy_error)}")
                return None

    def generate_dpa(self):
        """
        Generate the DPA document.

        Returns:
            str: Path to the generated DPA document
        """
        print("Generating DPA...")

        # Hardcode the template path that we know works with underscores instead of spaces
        template_path = "templates/base_templates/dpa/Template_Data_Processing_Agreement_DPA.docx"
        print(f"DPA template path (hardcoded): {template_path}")

        # Check if the template exists
        if not os.path.exists(template_path):
            print(f"DPA template not found at hardcoded path: {template_path}")
            # Try with the path from the engagement model as a fallback
            template_path = self.engagement.get_dpa_template_path()
            print(f"Trying DPA template path from model: {template_path}")

            # Resolve the template path for bundled environment
            template_path = self.resolve_template_path(template_path)
            print(f"Resolved DPA template path: {template_path}")

            # Check if the template exists
            if not os.path.exists(template_path):
                print(f"DPA template not found at resolved path: {template_path}")
                return None

        # Get the output path
        output_path = os.path.join(self.output_dir, self.engagement.get_dpa_filename())
        print(f"DPA output path: {output_path}")

        # Get the placeholders
        placeholders = self._get_placeholders()

        # Generate the document directly using DocxTemplate
        try:
            # Create the output directory if it doesn't exist
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            # Use centralized document generation function
            result = _generate_document_with_docxtpl(template_path, output_path, placeholders)
            if not result:
                print("DocxTemplate generation failed")
                return None

            print(f"DPA document generated successfully: {output_path}")

            # Verify the document was created
            if os.path.exists(output_path):
                # Debug the document
                debug_document(output_path, "DPA")
                return output_path
            else:
                print(f"DPA document generation failed: Output file not found at {output_path}")
                return None
        except Exception as e:
            print(f"Error generating DPA document: {str(e)}")
            import traceback
            traceback.print_exc()

            # As a last resort, try to copy the template to the output path
            try:
                shutil.copy2(template_path, output_path)
                print(f"Created a copy of the DPA template at {output_path} as a fallback")
                return output_path
            except Exception as copy_error:
                print(f"Error copying DPA template: {str(copy_error)}")
                return None

    def generate_taci_sow(self):
        """
        Generate the TACI SOW document.

        Returns:
            str: Path to the generated TACI SOW document
        """
        print("Generating TACI SOW...")

        # Get the template path
        template_path = self.engagement.get_taci_sow_template_path()
        print(f"TACI SOW template path: {template_path}")

        # Resolve the template path for bundled environment
        template_path = self.resolve_template_path(template_path)
        print(f"Resolved TACI SOW template path: {template_path}")

        # Check if the template exists
        if not os.path.exists(template_path):
            print(f"TACI SOW template not found: {template_path}")
            return None

        # Get the output path
        output_path = os.path.join(self.output_dir, self.engagement.get_taci_sow_filename())
        print(f"TACI SOW output path: {output_path}")

        # Get the placeholders
        placeholders = self._get_placeholders()

        # Use the wrapper function to generate the document
        result = generate_document_with_docxtpl(template_path, output_path, placeholders)

        if result:
            print(f"TACI SOW document generated successfully: {result}")
        else:
            print(f"TACI SOW document generation failed")

        return result

    def generate_rr_sow(self):
        """
        Generate the RR SOW document.

        Returns:
            str: Path to the generated RR SOW document
        """
        print("Generating RR SOW...")

        # Determine which template to use based on the insurance carrier
        if self.engagement.client and self.engagement.client.insurance_carrier == "Beazley":
            # Use the Beazley-specific template
            template_path = "templates/base_templates/rr/SOW_ON_Template_Beazley_Only_Recovery_&_Restoration_Support.docx"
            print(f"Using Beazley-specific RR SOW template: {template_path}")
        else:
            # Use the standard template
            template_path = "templates/base_templates/rr/SOW_ON_Template_Recovery_&_Restoration_Support.docx"
            print(f"Using standard RR SOW template: {template_path}")

        # Check if the template exists
        if not os.path.exists(template_path):
            print(f"RR SOW template not found at path: {template_path}")
            return None

        # Get the output path
        output_path = os.path.join(self.output_dir, self.engagement.get_rr_sow_filename())
        print(f"RR SOW output path: {output_path}")

        # Get the placeholders
        placeholders = self._get_placeholders()

        # Generate the document directly using DocxTemplate
        try:
            # Create the output directory if it doesn't exist
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            # Use centralized document generation function
            result = _generate_document_with_docxtpl(template_path, output_path, placeholders)
            if not result:
                print("DocxTemplate generation failed")
                return None

            print(f"RR SOW document generated successfully: {output_path}")

            # Verify the document was created
            if os.path.exists(output_path):
                # Debug the document
                debug_document(output_path, "RR SOW")
                return output_path
            else:
                print(f"RR SOW document generation failed: Output file not found at {output_path}")
                return None
        except Exception as e:
            print(f"Error generating RR SOW document: {str(e)}")
            import traceback
            traceback.print_exc()

            # As a last resort, try to copy the template to the output path
            try:
                shutil.copy2(template_path, output_path)
                print(f"Created a copy of the RR SOW template at {output_path} as a fallback")
                return output_path
            except Exception as copy_error:
                print(f"Error copying RR SOW template: {str(copy_error)}")
                return None

    def _get_ordinal_suffix(self, day):
        """Get the ordinal suffix for a day (1st, 2nd, 3rd, etc.)"""
        if 10 <= day % 100 <= 20:
            suffix = 'th'
        else:
            suffix = {1: 'st', 2: 'nd', 3: 'rd'}.get(day % 10, 'th')
        return suffix

    def _get_placeholders(self):
        """
        Get the placeholders for the document.

        Returns:
            dict: Dictionary of placeholders and values
        """
        # Get the basic placeholders from the engagement
        placeholders = self.engagement.get_placeholders()

        # Add today's date in different formats
        today_yyyymmdd = datetime.now().strftime("%Y%m%d")
        today_formatted = datetime.now().strftime("%B %d, %Y")  # Month Day, Year format
        
        # Add legal document date format
        today = datetime.now()
        day_ordinal = self._get_ordinal_suffix(today.day)
        legal_date = f"this {today.day}{day_ordinal} day of {today.strftime('%B')} {today.year}"

        placeholders['date'] = today_yyyymmdd
        placeholders['formatted_date'] = today_formatted
        placeholders['legal_date'] = legal_date

        # Special handling for endpoint count
        if hasattr(self.engagement, 'endpoint_count'):
            endpoint_count = self.engagement.endpoint_count
            # Format the endpoint count with commas for better readability
            placeholders['endpoint_count'] = f"{endpoint_count:,}"
            print(f"Endpoint count set to: {endpoint_count}")

            # Set the fixed fee price based on endpoint count
            if hasattr(self.engagement, 'is_fixed_fee') and self.engagement.is_fixed_fee:
                if endpoint_count <= 100:
                    placeholders['ffp_price'] = '$25,000'
                    placeholders['edr_soc_fee'] = '$2,500'
                    placeholders['total_costs'] = '$27,500'
                elif endpoint_count <= 250:
                    placeholders['ffp_price'] = '$55,000'
                    placeholders['edr_soc_fee'] = '$5,000'
                    placeholders['total_costs'] = '$60,000'
                elif endpoint_count <= 500:
                    placeholders['ffp_price'] = '$75,000'
                    placeholders['edr_soc_fee'] = '$11,250'
                    placeholders['total_costs'] = '$86,250'
                elif endpoint_count <= 750:
                    placeholders['ffp_price'] = '$90,000'
                    placeholders['edr_soc_fee'] = '$11,250'
                    placeholders['total_costs'] = '$101,250'
                elif endpoint_count <= 2000:
                    placeholders['ffp_price'] = '$115,000'
                    placeholders['edr_soc_fee'] = '$11,250'
                    placeholders['total_costs'] = '$126,250'
                else:
                    placeholders['ffp_price'] = '$125,000'
                    placeholders['edr_soc_fee'] = '$22,500'
                    placeholders['total_costs'] = '$147,500'

        # Comment out section replacements for now until we have the proper templates
        # Add email section content based on email platform
        # if hasattr(self.engagement, 'email_platform'):
        #     email_platform = self.engagement.email_platform
        #     print(f"Email platform set to: {email_platform}")

        #     # Determine the appropriate email section template
        #     email_section_content = self._get_email_section_content(email_platform)
        #     placeholders['email_section'] = email_section_content

        # Add EDR section content based on EDR monitoring selection
        # if hasattr(self.engagement, 'edr_monitoring'):
        #     edr_monitoring = self.engagement.edr_monitoring
        #     print(f"EDR monitoring set to: {edr_monitoring}")

        #     # Determine the appropriate EDR section template
        #     edr_section_content = self._get_edr_section_content(edr_monitoring)
        #     placeholders['edr_section'] = edr_section_content

        return placeholders

    def _get_sections_to_remove(self):
        """
        Get the sections to remove from the document.

        Returns:
            list: List of sections to remove
        """
        return self.engagement.get_sections_to_remove()

    def _get_email_section_content(self, email_platform):
        """
        Get the content for the email section based on the selected email platform.

        Args:
            email_platform (str): The selected email platform

        Returns:
            str: The content for the email section
        """
        # For now, just return a placeholder text to avoid issues with missing templates
        logger.info(f"Using placeholder text for email section: {email_platform}")
        return f"Email Analysis Section for {email_platform}\n\nThis is a placeholder for the email analysis section. The actual content will be included in the final document.\n\n"

    def _get_edr_section_content(self, edr_monitoring):
        """
        Get the content for the EDR section based on the selected EDR monitoring option.

        Args:
            edr_monitoring (str): The selected EDR monitoring option

        Returns:
            str: The content for the EDR section
        """
        # For now, just return a placeholder text to avoid issues with missing templates
        logger.info(f"Using placeholder text for EDR section: {edr_monitoring}")
        return f"EDR Monitoring Section for {edr_monitoring}\n\nThis is a placeholder for the EDR monitoring section. The actual content will be included in the final document.\n\n"
