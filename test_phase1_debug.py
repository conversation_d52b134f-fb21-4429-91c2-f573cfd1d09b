#!/usr/bin/env python3
"""
Debug script to test DFIR Phase 1 hours calculation
"""

# Test if we can import the function
try:
    from data.pricing_tables import get_phase_hours
    print("✓ Successfully imported get_phase_hours")
except ImportError as e:
    print(f"✗ Failed to import get_phase_hours: {e}")
    exit(1)

# Test if we can import the table directly
try:
    from data.pricing_tables import DFIR_PHASE1_HOURS
    print("✓ Successfully imported DFIR_PHASE1_HOURS")
    print(f"DFIR_PHASE1_HOURS = {DFIR_PHASE1_HOURS}")
except ImportError as e:
    print(f"✗ Failed to import DFIR_PHASE1_HOURS: {e}")
    
    # Try to access it through the module
    try:
        import data.pricing_tables as pt
        if hasattr(pt, 'DFIR_PHASE1_HOURS'):
            print("✓ DFIR_PHASE1_HOURS found as module attribute")
            print(f"DFIR_PHASE1_HOURS = {pt.DFIR_PHASE1_HOURS}")
        else:
            print("✗ DFIR_PHASE1_HOURS not found as module attribute")
            print(f"Available attributes: {[attr for attr in dir(pt) if 'DFIR' in attr]}")
    except Exception as e2:
        print(f"✗ Error accessing module: {e2}")

# Test the function with some values
test_cases = [
    (50, 40),
    (100, 40),
    (250, 50),
    (500, 50),
    (750, 75),
    (1000, 75)
]

print("\nTesting get_phase_hours function:")
for endpoints, expected in test_cases:
    try:
        actual = get_phase_hours(1, endpoints)
        status = "✓" if actual == expected else "✗"
        print(f"{status} {endpoints} endpoints: got {actual}, expected {expected}")
    except Exception as e:
        print(f"✗ Error testing {endpoints} endpoints: {e}")
