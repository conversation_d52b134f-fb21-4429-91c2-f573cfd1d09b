#!/usr/bin/env python3
"""
PACE Directory Cleanup Recommendations
Analyzes what files are essential vs. what can be removed.
"""

import os

def analyze_directory():
    """Analyze the directory and categorize files"""
    
    # Essential files for running PACE
    essential_core = [
        "main.py",                    # Main application entry point
        "requirements.txt",           # Python dependencies
        "PACE.ico",                   # Application icon
        "README.md",                  # Documentation
        "LICENSE.txt",                # License file
        "CHANGELOG.md",               # Version history
    ]
    
    essential_directories = [
        "data/",                      # Data files (pricing, carriers, law firms)
        "gui/",                       # GUI components
        "models/",                    # Data models
        "utils/",                     # Utility functions
        "templates/",                 # Document templates
        "resources/",                 # Resources (icons, etc.)
    ]
    
    # Build-related files (keep for development)
    build_files = [
        "build_executable.py",        # Build script
        "PACE.spec",                  # PyInstaller spec
        "version_info.txt",           # Version info for executable
        "PACE_installer.iss",         # Installer script
    ]
    
    # Distribution files (keep one copy)
    distribution_files = [
        "team_distribution/",         # Final distribution
        "dist/PACE.exe",              # Built executable
    ]
    
    # Files to DELETE (test files, temporary files, duplicates)
    files_to_delete = [
        # Test files
        "test_*.py",                  # All test files
        "debug_*.py",                 # Debug files
        "check_*.py",                 # Check files
        "fix_*.py",                   # Fix files
        "quick_test.py",              # Quick test
        
        # Temporary/generated documents
        "*.docx",                     # Test documents
        "*.log",                      # Log files
        
        # Icon creation files (keep only the final icon)
        "create_icon.py",
        "create_original_pace_icon.py",
        "create_proper_icon.py",
        "PACE_icon_preview.png",
        
        # Build artifacts
        "build/",                     # Build directory
        "__pycache__/",               # Python cache
        "*.pyc",                      # Compiled Python files
        
        # Duplicate distribution
        "distribution/",              # Old distribution folder
        
        # Extra batch files
        "build_auto_installer.bat",
        "build_complete.bat",
        "build_for_team.bat",
        "build_pace.bat",
        "build_installer.bat",
        
        # Extra documentation
        "DEPLOYMENT_GUIDE.md",
        "DEPLOYMENT_SUMMARY.md",
        "TEAM_DISTRIBUTION_GUIDE.md",
        "README.txt",                 # Duplicate of README.md
    ]
    
    print("PACE DIRECTORY CLEANUP RECOMMENDATIONS")
    print("=" * 60)
    
    print("\n✅ ESSENTIAL FILES TO KEEP:")
    print("-" * 30)
    for file in essential_core:
        status = "✓" if os.path.exists(file) else "✗ MISSING"
        print(f"  {file:<30} {status}")
    
    print("\n✅ ESSENTIAL DIRECTORIES TO KEEP:")
    print("-" * 35)
    for dir_name in essential_directories:
        status = "✓" if os.path.exists(dir_name.rstrip('/')) else "✗ MISSING"
        print(f"  {dir_name:<30} {status}")
    
    print("\n🔧 BUILD FILES (keep for development):")
    print("-" * 40)
    for file in build_files:
        status = "✓" if os.path.exists(file) else "✗ MISSING"
        print(f"  {file:<30} {status}")
    
    print("\n📦 DISTRIBUTION (keep one copy):")
    print("-" * 35)
    for item in distribution_files:
        status = "✓" if os.path.exists(item.rstrip('/')) else "✗ MISSING"
        print(f"  {item:<30} {status}")
    
    print("\n🗑️  FILES/FOLDERS TO DELETE:")
    print("-" * 30)
    
    import glob
    total_size = 0
    delete_count = 0
    
    for pattern in files_to_delete:
        matches = glob.glob(pattern, recursive=True)
        for match in matches:
            if os.path.exists(match):
                try:
                    if os.path.isfile(match):
                        size = os.path.getsize(match)
                        total_size += size
                        size_str = f"({size/1024:.1f} KB)"
                    else:
                        size_str = "(directory)"
                    print(f"  {match:<40} {size_str}")
                    delete_count += 1
                except:
                    print(f"  {match:<40} (error reading)")
    
    print(f"\n📊 CLEANUP SUMMARY:")
    print(f"  Files/folders to delete: {delete_count}")
    print(f"  Estimated space saved: {total_size/1024/1024:.1f} MB")
    
    print(f"\n💡 RECOMMENDATIONS:")
    print(f"  1. Keep the essential files and directories listed above")
    print(f"  2. Delete all test files (test_*.py, debug_*.py, etc.)")
    print(f"  3. Remove temporary documents (*.docx, *.log)")
    print(f"  4. Clean up build artifacts (__pycache__, build/)")
    print(f"  5. Remove duplicate distribution folders")
    print(f"  6. Keep only one copy of the executable in team_distribution/")
    
    return files_to_delete

def create_cleanup_script(files_to_delete):
    """Create a cleanup script"""
    
    cleanup_script = """@echo off
echo PACE Directory Cleanup
echo =====================

echo Cleaning up test files...
"""
    
    import glob
    for pattern in files_to_delete:
        matches = glob.glob(pattern, recursive=True)
        for match in matches:
            if os.path.exists(match):
                if os.path.isfile(match):
                    cleanup_script += f'if exist "{match}" del /f /q "{match}"\n'
                else:
                    cleanup_script += f'if exist "{match}" rmdir /s /q "{match}"\n'
    
    cleanup_script += """
echo Cleanup complete!
echo.
echo Remaining files are essential for PACE operation.
pause
"""
    
    with open("cleanup_pace_directory.bat", "w") as f:
        f.write(cleanup_script)
    
    print(f"\n✅ Created cleanup script: cleanup_pace_directory.bat")
    print(f"   Run this script to automatically clean up the directory")

if __name__ == "__main__":
    files_to_delete = analyze_directory()
    create_cleanup_script(files_to_delete)
