@echo off
echo PACE Installer Builder
echo ======================

REM Check if Inno Setup is installed
set "INNO_SETUP_PATH="
if exist "C:\Program Files (x86)\Inno Setup 6\ISCC.exe" (
    set "INNO_SETUP_PATH=C:\Program Files (x86)\Inno Setup 6\ISCC.exe"
) else if exist "C:\Program Files\Inno Setup 6\ISCC.exe" (
    set "INNO_SETUP_PATH=C:\Program Files\Inno Setup 6\ISCC.exe"
) else if exist "C:\Program Files (x86)\Inno Setup 5\ISCC.exe" (
    set "INNO_SETUP_PATH=C:\Program Files (x86)\Inno Setup 5\ISCC.exe"
) else if exist "C:\Program Files\Inno Setup 5\ISCC.exe" (
    set "INNO_SETUP_PATH=C:\Program Files\Inno Setup 5\ISCC.exe"
)

if "%INNO_SETUP_PATH%"=="" (
    echo ERROR: Inno Setup not found!
    echo Please install Inno Setup from: https://jrsoftware.org/isinfo.php
    echo.
    echo Expected locations:
    echo - C:\Program Files (x86)\Inno Setup 6\ISCC.exe
    echo - C:\Program Files\Inno Setup 6\ISCC.exe
    echo - C:\Program Files (x86)\Inno Setup 5\ISCC.exe
    echo - C:\Program Files\Inno Setup 5\ISCC.exe
    echo.
    pause
    exit /b 1
)

echo Found Inno Setup at: %INNO_SETUP_PATH%

REM Check if executable exists
if not exist "dist\PACE.exe" (
    echo ERROR: PACE.exe not found in dist folder!
    echo Please run build_executable.py first.
    pause
    exit /b 1
)

echo Found PACE.exe in dist folder

REM Create installer directory if it doesn't exist
if not exist "installer" mkdir installer

REM Build the installer
echo Building installer...
"%INNO_SETUP_PATH%" "PACE_installer.iss"

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ Installer built successfully!
    echo ✓ Location: installer\PACE_v1.1.4_Setup.exe
    echo.
    if exist "installer\PACE_v1.1.4_Setup.exe" (
        echo File size: 
        dir "installer\PACE_v1.1.4_Setup.exe" | findstr "PACE_v1.1.4_Setup.exe"
    )
) else (
    echo.
    echo ✗ Installer build failed!
    echo Check the error messages above.
)

echo.
pause
