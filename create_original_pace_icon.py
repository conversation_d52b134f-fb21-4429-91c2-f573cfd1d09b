#!/usr/bin/env python3
"""
Recreate the original PACE icon based on the provided design.
"""

import os
import math
from PIL import Image, ImageDraw, ImageFont

def create_original_pace_icon():
    """Create the original PACE icon with gear border and tower design"""
    try:
        # Create a 512x512 image for high quality
        size = 512
        img = Image.new('RGBA', (size, size), (0, 0, 0, 0))  # Transparent background
        draw = ImageDraw.Draw(img)
        
        center_x, center_y = size // 2, size // 2
        
        # Colors
        gear_color = (64, 224, 208)  # Teal/cyan color
        tower_color = (169, 169, 169)  # Silver/gray
        banner_color = (105, 105, 105)  # Dark gray
        text_color = (255, 255, 255)  # White
        background_color = (32, 32, 32)  # Dark background
        
        # Draw dark circular background
        bg_radius = 240
        draw.ellipse([center_x - bg_radius, center_y - bg_radius, 
                     center_x + bg_radius, center_y + bg_radius], 
                    fill=background_color)
        
        # Draw gear border (simplified gear teeth)
        gear_radius = 220
        inner_radius = 180
        num_teeth = 24
        
        # Create gear shape
        gear_points = []
        for i in range(num_teeth * 2):
            angle = (i * math.pi) / num_teeth
            if i % 2 == 0:
                # Outer tooth
                radius = gear_radius
            else:
                # Inner valley
                radius = inner_radius
            
            x = center_x + radius * math.cos(angle)
            y = center_y + radius * math.sin(angle)
            gear_points.append((x, y))
        
        # Draw gear outline
        draw.polygon(gear_points, outline=gear_color, width=4)
        
        # Draw inner circle
        inner_circle_radius = 160
        draw.ellipse([center_x - inner_circle_radius, center_y - inner_circle_radius,
                     center_x + inner_circle_radius, center_y + inner_circle_radius],
                    outline=gear_color, width=3)
        
        # Draw tower/antenna in center
        tower_width = 20
        tower_height = 80
        tower_top = center_y - 60
        tower_bottom = center_y + 20
        
        # Main tower shaft
        draw.rectangle([center_x - tower_width//2, tower_top,
                       center_x + tower_width//2, tower_bottom],
                      fill=tower_color)
        
        # Tower base (wider)
        base_width = 35
        base_height = 15
        draw.rectangle([center_x - base_width//2, tower_bottom,
                       center_x + base_width//2, tower_bottom + base_height],
                      fill=tower_color)
        
        # Tower top elements
        top_width = 30
        top_height = 8
        draw.rectangle([center_x - top_width//2, tower_top - top_height,
                       center_x + top_width//2, tower_top],
                      fill=tower_color)
        
        # Antenna lines radiating from tower
        line_length = 40
        for angle in [0, 45, 90, 135, 180, 225, 270, 315]:
            rad = math.radians(angle)
            start_x = center_x + 15 * math.cos(rad)
            start_y = center_y - 20 + 15 * math.sin(rad)
            end_x = center_x + line_length * math.cos(rad)
            end_y = center_y - 20 + line_length * math.sin(rad)
            draw.line([(start_x, start_y), (end_x, end_y)], fill=tower_color, width=2)
        
        # Try to load fonts
        try:
            # Try different font sizes for different text
            title_font = ImageFont.truetype("arial.ttf", 24)
            est_font = ImageFont.truetype("arial.ttf", 16)
            banner_font = ImageFont.truetype("arial.ttf", 14)
        except:
            try:
                title_font = ImageFont.truetype("calibri.ttf", 24)
                est_font = ImageFont.truetype("calibri.ttf", 16)
                banner_font = ImageFont.truetype("calibri.ttf", 14)
            except:
                title_font = ImageFont.load_default()
                est_font = ImageFont.load_default()
                banner_font = ImageFont.load_default()
        
        # Draw "EST 2025" at the top
        est_text = "EST 2025"
        est_bbox = draw.textbbox((0, 0), est_text, font=est_font)
        est_width = est_bbox[2] - est_bbox[0]
        est_x = center_x - est_width // 2
        est_y = center_y - 120
        draw.text((est_x, est_y), est_text, fill=text_color, font=est_font)
        
        # Draw "PACE" title
        pace_text = "PACE"
        pace_bbox = draw.textbbox((0, 0), pace_text, font=title_font)
        pace_width = pace_bbox[2] - pace_bbox[0]
        pace_x = center_x - pace_width // 2
        pace_y = center_y + 50
        draw.text((pace_x, pace_y), pace_text, fill=text_color, font=title_font)
        
        # Draw banner background for subtitle
        banner_width = 280
        banner_height = 30
        banner_x = center_x - banner_width // 2
        banner_y = center_y + 85
        
        # Draw banner rectangle
        draw.rectangle([banner_x, banner_y, banner_x + banner_width, banner_y + banner_height],
                      fill=banner_color, outline=gear_color, width=2)
        
        # Draw subtitle text on banner
        subtitle = "PROCESS AUTOMATION FOR CLIENT ENGAGEMENTS"
        # Split into two lines if too long
        line1 = "PROCESS AUTOMATION FOR"
        line2 = "CLIENT ENGAGEMENTS"
        
        # First line
        line1_bbox = draw.textbbox((0, 0), line1, font=banner_font)
        line1_width = line1_bbox[2] - line1_bbox[0]
        line1_x = center_x - line1_width // 2
        line1_y = banner_y + 3
        draw.text((line1_x, line1_y), line1, fill=text_color, font=banner_font)
        
        # Second line
        line2_bbox = draw.textbbox((0, 0), line2, font=banner_font)
        line2_width = line2_bbox[2] - line2_bbox[0]
        line2_x = center_x - line2_width // 2
        line2_y = banner_y + 16
        draw.text((line2_x, line2_y), line2, fill=text_color, font=banner_font)
        
        # Create multiple sizes for ICO format
        icon_sizes = [(16, 16), (32, 32), (48, 48), (64, 64), (128, 128), (256, 256)]
        images = []
        
        for icon_size in icon_sizes:
            resized = img.resize(icon_size, Image.Resampling.LANCZOS)
            images.append(resized)
        
        # Save as ICO file
        img.save('PACE_original.ico', format='ICO', sizes=[(s[0], s[1]) for s in icon_sizes])
        print("✓ Created PACE_original.ico")
        return True
        
    except Exception as e:
        print(f"✗ Error creating original icon: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function"""
    print("Recreating original PACE icon...")
    
    if create_original_pace_icon():
        print("✓ Original PACE icon recreated successfully!")
        print("✓ File saved as: PACE_original.ico")
        print("\nTo use this icon:")
        print("1. Rename PACE_original.ico to PACE.ico")
        print("2. Or copy it over the existing PACE.ico")
        print("3. Rebuild the executable")
        return True
    else:
        print("✗ Failed to recreate original icon")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
