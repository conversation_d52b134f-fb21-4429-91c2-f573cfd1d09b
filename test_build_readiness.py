#!/usr/bin/env python3
"""
Test script to verify PACE is ready for executable build.
Checks all dependencies and file requirements.
"""

import os
import sys
import importlib

def test_python_version():
    """Test Python version compatibility"""
    version = sys.version_info
    if version.major == 3 and version.minor >= 9:
        print(f"✓ Python {version.major}.{version.minor}.{version.micro} - Compatible")
        return True
    else:
        print(f"✗ Python {version.major}.{version.minor}.{version.micro} - Requires Python 3.9+")
        return False

def test_required_modules():
    """Test that all required modules are available"""
    required_modules = [
        'PySide6',
        'docxtpl', 
        'docx',
        'docxcompose',
        'jinja2',
        'lxml',
        'PIL'
    ]
    
    all_good = True
    for module in required_modules:
        try:
            importlib.import_module(module)
            print(f"✓ {module} - Available")
        except ImportError:
            print(f"✗ {module} - Missing (run: pip install {module})")
            all_good = False
    
    return all_good

def test_required_files():
    """Test that all required files exist"""
    required_files = [
        'main.py',
        'PACE.ico',
        'requirements.txt',
        'README.md',
        'CHANGELOG.md'
    ]
    
    required_dirs = [
        'templates',
        'data', 
        'models',
        'utils',
        'gui'
    ]
    
    all_good = True
    
    # Check files
    for file in required_files:
        if os.path.exists(file):
            print(f"✓ {file} - Found")
        else:
            print(f"✗ {file} - Missing")
            all_good = False
    
    # Check directories
    for dir in required_dirs:
        if os.path.isdir(dir):
            print(f"✓ {dir}/ - Found")
        else:
            print(f"✗ {dir}/ - Missing")
            all_good = False
    
    return all_good

def test_templates():
    """Test that template files exist"""
    template_dirs = [
        'templates/base_templates/dfir',
        'templates/base_templates/taci',
        'templates/base_templates/rr',
        'templates/base_templates/bec',
        'templates/base_templates/msa_templates'
    ]
    
    all_good = True
    template_count = 0
    
    for template_dir in template_dirs:
        if os.path.isdir(template_dir):
            files = [f for f in os.listdir(template_dir) if f.endswith('.docx')]
            template_count += len(files)
            print(f"✓ {template_dir} - {len(files)} templates")
        else:
            print(f"✗ {template_dir} - Missing")
            all_good = False
    
    print(f"✓ Total templates found: {template_count}")
    return all_good and template_count > 0

def test_application_startup():
    """Test that the application can start without errors"""
    try:
        # Import main modules
        from main import __version__
        from models.client import Client
        from models.dfir import DFIREngagement
        from utils.document_generator import DocumentGenerator
        
        print(f"✓ Application version: {__version__}")
        print("✓ Core modules import successfully")
        
        # Test basic functionality
        client = Client()
        client.name = "Test Client"
        
        dfir = DFIREngagement(client)
        dfir.endpoint_count = 100
        
        print("✓ Basic object creation works")
        return True
        
    except Exception as e:
        print(f"✗ Application startup test failed: {e}")
        return False

def estimate_build_size():
    """Estimate the size of the final executable"""
    try:
        import site
        site_packages = site.getsitepackages()[0]
        
        # Estimate sizes of major dependencies
        dependencies = {
            'PySide6': 150,  # MB
            'docxtpl': 5,
            'python-docx': 10,
            'jinja2': 5,
            'lxml': 15,
            'PIL': 10
        }
        
        total_deps = sum(dependencies.values())
        
        # Add application files
        app_size = 0
        for root, dirs, files in os.walk('.'):
            if 'venv' in root or '__pycache__' in root:
                continue
            for file in files:
                try:
                    app_size += os.path.getsize(os.path.join(root, file))
                except:
                    pass
        
        app_size_mb = app_size / (1024 * 1024)
        estimated_total = total_deps + app_size_mb + 20  # 20MB buffer
        
        print(f"✓ Estimated executable size: {estimated_total:.0f} MB")
        print(f"  - Dependencies: {total_deps} MB")
        print(f"  - Application: {app_size_mb:.1f} MB")
        print(f"  - Overhead: 20 MB")
        
        return True
        
    except Exception as e:
        print(f"✗ Size estimation failed: {e}")
        return False

def main():
    """Main test function"""
    print("PACE v1.1.4 - Build Readiness Test")
    print("=" * 50)
    
    tests = [
        ("Python Version", test_python_version),
        ("Required Modules", test_required_modules),
        ("Required Files", test_required_files),
        ("Template Files", test_templates),
        ("Application Startup", test_application_startup),
        ("Build Size Estimate", estimate_build_size)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 30)
        if test_func():
            passed += 1
        else:
            print(f"✗ {test_name} failed")
    
    print("\n" + "=" * 50)
    print(f"BUILD READINESS: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ READY TO BUILD - All tests passed!")
        print("\nNext steps:")
        print("1. Run: python build_executable.py")
        print("2. Or double-click: build_pace.bat")
        print("3. Test: dist/PACE.exe")
        return True
    else:
        print("✗ NOT READY - Fix the issues above first")
        print("\nCommon fixes:")
        print("- Install missing modules: pip install -r requirements.txt")
        print("- Ensure all template files are present")
        print("- Check file permissions")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
