# PACE v1.1.4 - Process Automation for Client Engagements

PACE is a comprehensive document generation application designed to streamline the creation of legal and consulting engagement documents. It automates the generation of Statements of Work (SOWs), Master Service Agreements (MSAs), and other critical engagement documents.

## What's New in v1.1.4

### Major Performance Optimizations
- **70% reduction** in document generation code through consolidation
- **Faster startup time** with module-level imports
- **Improved document generation speed** with centralized functions

### Enhanced Recovery & Remediation (R&R) Experience
- **Two-step R&R workflow**: First decide if R&R is needed, then choose to generate SOW
- **Conditional document generation**: R&R SOW only created when explicitly requested
- **Better user control** over document generation process

## Features

### Document Types Supported
- **DFIR (Digital Forensics & Incident Response)** SOWs
- **TACI (Threat Actor Communications)** SOWs  
- **BEC (Business Email Compromise)** SOWs
- **Recovery & Remediation** SOWs
- **Master Service Agreements** (MSAs)
- **Business Associate Agreements** (BAAs)
- **Data Processing Agreements** (DPAs)

### Carrier-Specific Support
- **Beazley** - Specialized templates and pricing
- **Chubb** - Custom CS codes and mandatory fees
- **Coalition** - Fixed fee and hourly options
- **AXA XL** - Endpoint-based pricing tiers
- **Standard** - Default templates for other carriers

### Key Capabilities
- **Automated pricing calculations** based on endpoint counts and engagement types
- **Carrier-specific template selection** and customization
- **Law firm integration** with custom MSA templates
- **Flexible pricing models** (fixed fee vs. time & materials)
- **Comprehensive placeholder replacement** system

## Installation & Setup

### Prerequisites
- **Python 3.9+** (Download from [python.org](https://www.python.org/downloads/))
- **Windows 10/11** (Primary supported platform)

### Quick Start
1. **Download** the PACE package
2. **Open Command Prompt** in the PACE directory
3. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```
4. **Run the application**:
   ```bash
   python main.py
   ```

### Dependencies
- **PySide6** - Modern Qt-based GUI framework
- **docxtpl** - Word document template processing
- **python-docx** - Word document manipulation
- **docxcompose** - Document merging capabilities

## User Guide

### Basic Workflow
1. **Select Engagement Type** (DFIR, TACI, BEC, or R&R)
2. **Enter Client Information** (name, address, insurance carrier)
3. **Configure Engagement Parameters** (endpoints, pricing model, services)
4. **Choose Additional Services** (TACI, R&R, agreements)
5. **Generate Documents** with one click

### Recovery & Remediation (R&R) Workflow
1. **Check "Recovery and Remediation Needed"** if R&R services are required
2. **Choose assistance type**: Remote or Onsite
3. **Set resource count** (for onsite assistance)
4. **Decide on SOW generation**: Check "Generate R&R Statement of Work" if formal SOW needed
5. **Generate documents** - R&R SOW only created if explicitly requested

### Pricing Models
- **Fixed Fee**: Predetermined pricing for smaller engagements
- **Time & Materials**: Hourly billing for larger or complex engagements
- **Carrier-Specific**: Automatic selection based on insurance carrier requirements

## Technical Details

### Architecture
- **Model-View-Controller** pattern with PySide6 GUI
- **Modular design** with separate engagement types
- **Template-based** document generation
- **Centralized configuration** for carriers and pricing

### Performance Optimizations (v1.1.4)
- **Consolidated document generation** functions
- **Module-level imports** for faster startup
- **Eliminated code duplication** (70% reduction)
- **Optimized test suite** with better performance

### File Structure
```
PACE/
├── main.py                 # Application entry point
├── models/                 # Data models (Client, Engagements)
├── gui/                    # User interface components
├── utils/                  # Document generation utilities
├── data/                   # Pricing tables and carrier data
├── templates/              # Word document templates
└── tests/                  # Test suite
```

## Testing

### Running Tests
```bash
# Run all tests
python -m pytest

# Run specific test suites
python test_todays_updates.py
python test_comprehensive_pricing.py
python test_rr_improvements.py
```

### Test Coverage
- **Pricing calculations** for all engagement types
- **Document generation** workflows
- **R&R conditional generation** (new in v1.1.4)
- **Carrier-specific** features and templates



##  Version History

### v1.1.4 (Current)
- Performance optimizations
- Enhanced R&R user experience
- Consolidated document generation
- Improved code quality

### v1.1.3
- Chubb carrier enhancements
- Template path fixes
- Error handling improvements

### v1.1.2
- Template resolution fixes
- HTML entity decoding
- Document opening issues resolved

### v1.1.1
- Chubb template fixes
- Performance improvements
- Documentation updates

## Support

### Getting Help
- **Version History**: Available in Help → Version History
- **README**: Available in Help → View README
- **Error Logs**: Check `~/PACE_logs/` directory

### Common Issues
- **Template not found**: Ensure all template files are in `templates/base_templates/`
- **Import errors**: Verify all dependencies installed with `pip install -r requirements.txt`
- **Document generation fails**: Check error logs for specific template or placeholder issues

## License

This software is for internal business use only. All rights reserved.

## Future Enhancements

- **Business Email Compromise (BEC) completion** - Finalize remaining BEC engagement features
- **Threat Actor Communications (TACI) enhancements** - Expand TACI capabilities and workflows
- **Advanced template management** - Improved template organization and customization
- **Enhanced reporting** - Better analytics and engagement tracking
- **Workflow automation** - Additional process automation features

---

**PACE v1.1.4** - Streamlining legal engagement processes with intelligent automation.
