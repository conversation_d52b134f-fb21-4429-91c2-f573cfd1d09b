#!/usr/bin/env python3
"""
Test script to verify the built PACE.exe works correctly.
"""

import os
import subprocess
import time
from pathlib import Path

def test_executable_exists():
    """Test that the executable was created"""
    exe_path = Path("dist/PACE.exe")
    if exe_path.exists():
        size_mb = exe_path.stat().st_size / (1024 * 1024)
        print(f"✓ PACE.exe found: {size_mb:.1f} MB")
        return True
    else:
        print("✗ PACE.exe not found in dist/ folder")
        return False

def test_executable_runs():
    """Test that the executable can start"""
    try:
        print("Testing executable startup...")
        
        # Try to run the executable with a timeout
        # Since it's a GUI app, it will start and we'll kill it after a few seconds
        process = subprocess.Popen(
            ["dist\\PACE.exe"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            creationflags=subprocess.CREATE_NO_WINDOW
        )
        
        # Wait a few seconds for startup
        time.sleep(5)
        
        # Check if process is still running (good sign)
        if process.poll() is None:
            print("✓ Executable started successfully")
            # Terminate the process
            process.terminate()
            process.wait(timeout=5)
            return True
        else:
            # Process exited, check for errors
            stdout, stderr = process.communicate()
            if process.returncode == 0:
                print("✓ Executable ran and exited cleanly")
                return True
            else:
                print(f"✗ Executable failed with return code: {process.returncode}")
                if stderr:
                    print(f"Error: {stderr.decode()}")
                return False
                
    except subprocess.TimeoutExpired:
        print("✓ Executable is running (timeout reached, terminating)")
        process.terminate()
        return True
    except Exception as e:
        print(f"✗ Error testing executable: {e}")
        return False

def test_build_artifacts():
    """Test that all build artifacts were created"""
    required_files = [
        "PACE.spec",
        "version_info.txt", 
        "LICENSE.txt",
        "PACE_installer.iss"
    ]
    
    all_good = True
    for file in required_files:
        if os.path.exists(file):
            print(f"✓ {file} - Created")
        else:
            print(f"✗ {file} - Missing")
            all_good = False
    
    return all_good

def test_installer_script():
    """Test that the installer script is valid"""
    try:
        with open("PACE_installer.iss", "r") as f:
            content = f.read()
            
        # Check for key sections
        required_sections = [
            "[Setup]",
            "[Files]", 
            "[Icons]",
            "AppVersion=1.1.4",
            "PACE.exe"
        ]
        
        all_good = True
        for section in required_sections:
            if section in content:
                print(f"✓ Installer contains: {section}")
            else:
                print(f"✗ Installer missing: {section}")
                all_good = False
        
        return all_good
        
    except Exception as e:
        print(f"✗ Error reading installer script: {e}")
        return False

def test_file_sizes():
    """Test that file sizes are reasonable"""
    try:
        exe_path = Path("dist/PACE.exe")
        if exe_path.exists():
            size_mb = exe_path.stat().st_size / (1024 * 1024)
            
            # Reasonable size range: 50-300 MB
            if 50 <= size_mb <= 300:
                print(f"✓ Executable size reasonable: {size_mb:.1f} MB")
                return True
            else:
                print(f"⚠ Executable size unusual: {size_mb:.1f} MB")
                return True  # Still pass, just warn
        else:
            print("✗ Executable not found for size check")
            return False
            
    except Exception as e:
        print(f"✗ Error checking file size: {e}")
        return False

def main():
    """Main test function"""
    print("PACE v1.1.4 - Executable Test")
    print("=" * 40)
    
    tests = [
        ("Executable Exists", test_executable_exists),
        ("Build Artifacts", test_build_artifacts),
        ("Installer Script", test_installer_script),
        ("File Sizes", test_file_sizes),
        ("Executable Runs", test_executable_runs)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 25)
        if test_func():
            passed += 1
        else:
            print(f"✗ {test_name} failed")
    
    print("\n" + "=" * 40)
    print(f"EXECUTABLE TEST: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ BUILD SUCCESSFUL - Executable ready for deployment!")
        print("\nDeployment options:")
        print("1. Standalone: Distribute dist/PACE.exe")
        print("2. Installer: Compile PACE_installer.iss with Inno Setup")
        print("3. Test on clean machine without Python")
        return True
    else:
        print("✗ BUILD ISSUES - Check the failures above")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
