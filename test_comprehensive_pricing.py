"""
Comprehensive test for all PACE pricing calculations including:
- DFIR Phase I SOW pricing
- TACI/Ransom Communications updated hours
- Recovery & Remediation updated hours
"""

def test_comprehensive_pricing():
    """Test all pricing calculations comprehensively."""
    print("PACE Comprehensive Pricing Test")
    print("=" * 60)
    
    try:
        from models.dfir import DFIREngagement
        from models.rr import RREngagement
        from models.taci import TACIEngagement
        from models.client import Client
        
        # Test scenarios
        carriers = ["Standard", "Chubb", "Coalition", "AXA XL", "Beazley"]
        endpoint_counts = [50, 500, 1500, 3000]
        
        print("\n1. DFIR PHASE I SOW PRICING")
        print("-" * 40)
        
        for carrier in carriers:
            for endpoints in endpoint_counts:
                print(f"\n{carrier} - {endpoints:,} endpoints:")
                
                client = Client()
                if carrier != "Standard":
                    client.insurance_carrier = carrier
                
                dfir = DFIREngagement(client)
                dfir.endpoint_count = endpoints
                dfir.edr_monitoring = "Monitoring and Threat Hunting"
                
                pricing = dfir.calculate_pricing()
                placeholders = dfir.get_placeholders()
                
                # Show key pricing info - check multiple possible keys
                rate = pricing.get("dfir_rate", 450)
                if isinstance(rate, str):
                    rate = float(rate.replace("$", "").replace(",", ""))
                
                total_hours = pricing.get("sp_total_hours", 0)
                if total_hours == 0:
                    total_hours = pricing.get("total_hours", 0)
                
                total_cost = pricing.get("sp_costs", "$0")
                if total_cost == "$0":
                    total_cost = pricing.get("total_cost", "$0")
                
                print(f"  Rate: ${rate}/hr")
                print(f"  Total Hours: {total_hours}")
                print(f"  Total Cost: {total_cost}")
                
                # Debug: Show all pricing keys for troubleshooting
                if total_hours == 0:
                    print(f"  DEBUG - Available pricing keys: {list(pricing.keys())}")
        
        print("\n\n2. TACI/RANSOM COMMUNICATIONS UPDATED HOURS")
        print("-" * 50)
        
        for carrier in carriers:
            print(f"\n{carrier} Carrier:")
            
            client = Client()
            if carrier != "Standard":
                client.insurance_carrier = carrier
            
            taci = TACIEngagement(client)
            taci.is_ransom_communications = True
            
            pricing = taci.calculate_pricing()
            
            # Get the actual calculated values from TACI pricing
            # The TACI model calculates these based on carrier-specific rates

            # Determine which pricing keys to use based on carrier
            if client.law_firm == "The Beckage Firm":
                phase1_key = "beckage_taci_phase1_costs"
                phase2_key = "beckage_taci_phase2_costs"
                total_key = "beckage_taci_total_cost"
            elif client.insurance_carrier == "Beazley":
                phase1_key = "beazley_taci_phase1_costs"
                phase2_key = "beazley_taci_phase2_costs"
                total_key = "beazley_taci_total_cost"
            else:
                phase1_key = "taci_phase1_costs"
                phase2_key = "taci_phase2_costs"
                total_key = "taci_total_cost"

            # Get the calculated costs from pricing
            actual_phase1 = pricing.get(phase1_key, "")
            actual_phase2 = pricing.get(phase2_key, "")
            actual_total = pricing.get(total_key, "")

            # Get the rate for display (this is the formatted rate)
            rate_display = pricing.get("taci_rate", "$450")

            print(f"  Rate: {rate_display}/hr")
            print(f"  Phase I: 40 hours = {actual_phase1}")
            print(f"  Phase II: 25 hours = {actual_phase2}")
            print(f"  Total: 65 hours = {actual_total}")

            # For this test, we just verify that the values are present and formatted correctly
            status1 = "✓" if actual_phase1 and actual_phase1.startswith("$") else "✗"
            status2 = "✓" if actual_phase2 and actual_phase2.startswith("$") else "✗"
            status3 = "✓" if actual_total and actual_total.startswith("$") else "✗"

            print(f"  {status1} Phase I Cost: {actual_phase1}")
            print(f"  {status2} Phase II Cost: {actual_phase2}")
            print(f"  {status3} Total Cost: {actual_total}")
        
        print("\n\n3. RECOVERY & REMEDIATION UPDATED HOURS")
        print("-" * 45)
        
        rr_scenarios = [
            ("Remote RR", True, 1, None, 150),
            ("Onsite RR - 1 Resource", False, 1, None, 200),
            ("Onsite RR - 2 Resources", False, 2, None, 350),  # 200 + 150
            ("Onsite RR - 3 Resources", False, 3, None, 500),  # 200 + 150 + 150
            ("Beazley Onsite - 1 Resource", False, 1, "Beazley", 150),
            ("Beazley Onsite - 2 Resources", False, 2, "Beazley", 250),  # 150 + 100
            ("Beazley Onsite - 3 Resources", False, 3, "Beazley", 350),  # 150 + 100 + 100
        ]
        
        for scenario, is_remote, resources, carrier, expected_hours in rr_scenarios:
            print(f"\n{scenario}:")
            
            client = Client()
            if carrier:
                client.insurance_carrier = carrier
            
            rr = RREngagement(client)
            rr.is_remote = is_remote
            rr.resource_count = resources
            
            pricing = rr.calculate_pricing()
            
            rate = pricing.get("rr_rate", 275)
            actual_hours = pricing.get("rr_total_hours", 0)
            total_cost = pricing.get("rr_total_cost", "$0")
            
            status = "✓" if actual_hours == expected_hours else "✗"
            
            print(f"  {status} Rate: ${rate}/hr")
            print(f"  {status} Hours: {actual_hours} (expected: {expected_hours})")
            print(f"  {status} Cost: {total_cost}")
        
        print("\n\n4. DFIR WITH RR INTEGRATION")
        print("-" * 35)
        
        integration_scenarios = [
            ("DFIR + Remote RR", True, 1, None),
            ("DFIR + Onsite RR (2 resources)", False, 2, None),
            ("DFIR + Beazley Onsite RR (2 resources)", False, 2, "Beazley"),
        ]
        
        for scenario, rr_remote, resources, carrier in integration_scenarios:
            print(f"\n{scenario}:")
            
            client = Client()
            if carrier:
                client.insurance_carrier = carrier
            
            dfir = DFIREngagement(client)
            dfir.endpoint_count = 500
            dfir.include_rr = True
            dfir.rr_is_remote = rr_remote
            dfir.onsite_resources_count = resources
            
            placeholders = dfir.get_placeholders()
            
            # Check RR placeholders
            rr_hours = placeholders.get("rr_total_hours", "0")
            rr_cost = placeholders.get("rr_total_cost", "$0")
            rr_type = placeholders.get("rr_type", "Unknown")
            
            print(f"  RR Type: {rr_type}")
            print(f"  RR Hours: {rr_hours}")
            print(f"  RR Cost: {rr_cost}")
            print(f"  Resources: {resources}")
            
            # Show Beazley-specific placeholders if applicable
            if carrier == "Beazley":
                beazley_additional = placeholders.get("beazley_ir_phase2_additional_hours", "0")
                print(f"  Beazley IR Phase 2 Additional Hours: {beazley_additional}")
        
        print("\n\nTest completed successfully! ✓")
        return True
        
    except Exception as e:
        print(f"\n✗ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_comprehensive_pricing()

