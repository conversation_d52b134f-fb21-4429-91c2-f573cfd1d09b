"""
DFIR screen UI for PACE application.
"""

import sys
from PySide6.QtWidgets import (
    QWidget, QLabel, QLineEdit, QComboBox, QCheckBox, QSpinBox,
    QPushButton, QVBoxLayout, QHBoxLayout, QFormLayout, QGroupBox,
    QButtonGroup, QApplication, QScrollArea, QSizePolicy, QRadioButton
)
from PySide6.QtCore import Qt, Signal

from gui.custom_radio_button import CustomRadioButton

from models.client import Client
from models.dfir import DFIREngagement

class DFIRScreen(QWidget):
    """
    DFIR screen for the PACE application.
    """

    # Signal emitted when the user clicks the Generate Documents button
    generate_clicked = Signal(DFIREngagement)

    # Signal emitted when the user clicks the Back button
    back_clicked = Signal()

    def __init__(self, client):
        """
        Initialize the DFIR screen.

        Args:
            client (Client): The client for this engagement
        """
        super().__init__()

        self.client = client
        self.engagement = DFIREngagement(client)

        # Check if this is a Beazley client
        self.is_beazley_client = (client and client.insurance_carrier == "Beazley")

        # Initialize event tracking
        self.event_count = 0

        self.init_ui()

    def init_ui(self):
        """
        Initialize the UI.
        """
        # Set up the main layout
        main_layout = QVBoxLayout()
        main_layout.setSpacing(15)  # Add more spacing between elements

        # Add the title
        title_label = QLabel("DFIR (Digital Forensics Incident Response)")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold;")
        main_layout.addWidget(title_label)

        # Add the client information
        client_info_label = QLabel(f"Client: {self.client.name}")
        client_info_label.setStyleSheet("font-weight: bold;")
        main_layout.addWidget(client_info_label)

        # Add a separator
        main_layout.addSpacing(20)

        # Create a scroll area for the form
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QScrollArea.NoFrame)  # Remove the frame

        # Create a widget to hold the form
        form_widget = QWidget()

        # Create a form layout for the DFIR parameters
        form_layout = QFormLayout(form_widget)
        form_layout.setSpacing(15)  # Add more spacing between form elements

        # Add the endpoint count field
        self.endpoint_count_spin = QSpinBox()
        self.endpoint_count_spin.setRange(1, 100000)
        self.endpoint_count_spin.setValue(100)
        form_layout.addRow("Enter number of total endpoints:", self.endpoint_count_spin)

        # Add the pricing model options
        pricing_group = QGroupBox("Pricing Model")
        pricing_layout = QHBoxLayout()

        self.fixed_fee_radio = QRadioButton("Fixed Fee")
        self.time_materials_radio = QRadioButton("Time and Materials")
        self.time_materials_radio.setChecked(True)

        pricing_layout.addWidget(self.fixed_fee_radio)
        pricing_layout.addWidget(self.time_materials_radio)

        pricing_group.setLayout(pricing_layout)
        form_layout.addRow("", pricing_group)

        # Add the email platform dropdown
        self.email_combo = QComboBox()
        self.email_combo.addItems(["M365", "Google", "Exchange", "Hybrid", "Other"])
        self.email_combo.setCurrentText(self.engagement.email_platform)
        self.email_combo.currentTextChanged.connect(self.update_email_platform)
        form_layout.addRow("Email Platform:", self.email_combo)

        # Add the EDR monitoring dropdown
        self.edr_combo = QComboBox()
        self.edr_combo.addItems([
            "Monitoring and Threat Hunting New Console",
            "Monitoring and Threat Hunting Existing Console",
            "Threat Hunting Only",
            "Monitoring by Client",
            "None"
        ])
        self.edr_combo.setCurrentText(self.engagement.edr_monitoring)
        self.edr_combo.currentTextChanged.connect(self.update_edr_monitoring)
        form_layout.addRow("EDR Monitoring:", self.edr_combo)

        # Add Beazley-specific ransomware/network intrusion checkbox if this is a Beazley client
        if self.is_beazley_client:
            self.ransomware_group = QGroupBox("Incident Type")
            ransomware_layout = QVBoxLayout()

            self.ransomware_check = QCheckBox("Ransomware or Network Intrusion")
            self.ransomware_check.setToolTip("Check this if the incident involves ransomware or network intrusion")
            self.ransomware_check.stateChanged.connect(self.update_ransomware_flag)

            # Add an info label explaining the significance
            info_label = QLabel("Note: Beazley uses fixed fee pricing for ransomware/network intrusion incidents")
            info_label.setStyleSheet("color: #4a6fa5; font-style: italic; font-size: 9pt;")
            info_label.setWordWrap(True)

            ransomware_layout.addWidget(self.ransomware_check)
            ransomware_layout.addWidget(info_label)

            self.ransomware_group.setLayout(ransomware_layout)
            form_layout.addRow("", self.ransomware_group)

        # Add the additional services checkboxes
        services_group = QGroupBox("Additional Services")
        services_layout = QVBoxLayout()

        self.taci_check = QCheckBox("Threat Actor Communications")
        self.rr_check = QCheckBox("Recovery and Remediation Needed")

        services_layout.addWidget(self.taci_check)
        services_layout.addWidget(self.rr_check)

        services_group.setLayout(services_layout)
        form_layout.addRow("", services_group)

        # Add the RR assistance options
        self.rr_group = QGroupBox("Recovery and Remediation Options")
        rr_layout = QVBoxLayout()

        # Add checkbox to determine if R&R SOW should be generated
        self.rr_generate_check = QCheckBox("Generate R&R Statement of Work")
        rr_layout.addWidget(self.rr_generate_check)

        # Create a horizontal layout for the assistance type buttons
        assistance_layout = QHBoxLayout()
        assistance_label = QLabel("Assistance Type:")
        assistance_layout.addWidget(assistance_label)

        # Create standard radio buttons
        self.remote_radio = QRadioButton("Remote Assistance")
        self.onsite_radio = QRadioButton("Onsite Assistance")

        # Create a button group with exclusive set to True for proper radio button behavior
        self.button_group = QButtonGroup(self)
        self.button_group.addButton(self.remote_radio)
        self.button_group.addButton(self.onsite_radio)
        self.button_group.setExclusive(True)

        # Add the buttons to the layout
        assistance_layout.addWidget(self.remote_radio)
        assistance_layout.addWidget(self.onsite_radio)

        rr_layout.addLayout(assistance_layout)

        # Create a horizontal layout for the resource count (only for onsite)
        resource_layout = QHBoxLayout()
        self.resource_label = QLabel("Number of Onsite Resources:")

        # Create and configure the resource count spinner
        self.resource_spin = QSpinBox()
        self.resource_spin.setRange(1, 10)  # Allow 1-10 resources
        self.resource_spin.setValue(1)      # Default to 1 resource
        self.resource_spin.setReadOnly(False)  # Make sure it's not read-only
        self.resource_spin.setButtonSymbols(QSpinBox.UpDownArrows)  # Show up/down arrows
        self.resource_spin.setWrapping(False)  # Don't wrap around at min/max
        self.resource_spin.setAccelerated(True)  # Allow accelerated stepping
        self.resource_spin.setKeyboardTracking(True)  # Update value while typing

        # Set a minimum width to ensure the spinner is visible
        self.resource_spin.setMinimumWidth(80)

        # Add the resource count to the layout
        resource_layout.addWidget(self.resource_label)
        resource_layout.addWidget(self.resource_spin)

        rr_layout.addLayout(resource_layout)

        # Set up the initial state
        self.remote_radio.setChecked(True)
        self.resource_spin.setEnabled(False)  # Only enabled for onsite assistance

        # Install event filter to handle radio button events
        self.remote_radio.installEventFilter(self)
        self.onsite_radio.installEventFilter(self)

        self.rr_group.setLayout(rr_layout)
        form_layout.addRow("", self.rr_group)

        # Add the agreement checkboxes
        agreements_group = QGroupBox("Required Agreements")
        agreements_layout = QVBoxLayout()

        self.dpa_check = QCheckBox("Data Processing Agreement (DPA)")
        self.baa_check = QCheckBox("Business Associate Agreement (BAA)")

        agreements_layout.addWidget(self.dpa_check)
        agreements_layout.addWidget(self.baa_check)

        agreements_group.setLayout(agreements_layout)
        form_layout.addRow("", agreements_group)

        # Set the form widget as the scroll area's widget
        scroll_area.setWidget(form_widget)

        # Add the scroll area to the main layout
        main_layout.addWidget(scroll_area, 1)  # Give the scroll area a stretch factor of 1

        # Add a separator
        main_layout.addSpacing(20)

        # Add the buttons
        button_layout = QHBoxLayout()

        self.back_button = QPushButton("Back")
        self.back_button.clicked.connect(self.on_back_clicked)

        self.generate_button = QPushButton("Generate Documents")
        self.generate_button.clicked.connect(self.on_generate_clicked)

        button_layout.addWidget(self.back_button)
        button_layout.addStretch()
        button_layout.addWidget(self.generate_button)

        main_layout.addLayout(button_layout)

        # Set the main layout
        self.setLayout(main_layout)

        # Set the window properties
        self.setWindowTitle("PACE - DFIR")
        self.resize(800, 700)  # Increased size for better visibility

        # Connect signals
        # Endpoint count
        self.endpoint_count_spin.valueChanged.connect(self.update_endpoint_count)

        # Pricing model
        self.fixed_fee_radio.toggled.connect(self.update_pricing_model)
        self.time_materials_radio.toggled.connect(self.update_pricing_model)

        # Email platform already connected in init_ui

        # EDR monitoring already connected in init_ui

        # Additional services
        self.taci_check.stateChanged.connect(self.update_taci)
        self.rr_check.stateChanged.connect(self.on_rr_check_changed)
        self.rr_generate_check.stateChanged.connect(self.on_rr_generate_check_changed)

        # Additional agreements
        self.baa_check.stateChanged.connect(self.update_baa)
        self.dpa_check.stateChanged.connect(self.update_dpa)

        # Connect the resource count spinner
        self.resource_spin.valueChanged.connect(self.update_onsite_resources)

        # Initially disable the RR assistance options
        self.rr_group.setEnabled(False)

        # Initialize the radio buttons and resource count
        self.remote_radio.setChecked(True)
        self.onsite_radio.setChecked(False)
        self.resource_spin.setValue(1)
        self.resource_spin.setEnabled(False)
        self.resource_label.setEnabled(False)
        self.rr_generate_check.setChecked(False)
        self.rr_generate_check.setEnabled(False)

    def on_rr_check_changed(self, state):
        """
        Handle changes to the Recovery and Remediation Needed checkbox.

        Args:
            state (int): The checkbox state
        """
        is_checked = state == Qt.Checked

        # Enable/disable the RR assistance options group
        self.rr_group.setEnabled(is_checked)

        # Enable/disable the generate R&R SOW checkbox
        self.rr_generate_check.setEnabled(is_checked)

        # Reset states when enabling
        if is_checked:
            # Default to remote assistance
            self.remote_radio.setChecked(True)
            self.onsite_radio.setChecked(False)

            # Disable resource count for remote assistance
            self.resource_spin.setValue(1)
            self.resource_spin.setEnabled(False)
            self.resource_label.setEnabled(False)

            # Update the engagement model
            self.engagement.rr_is_remote = True
            self.engagement.onsite_resources_count = 1

            # Don't automatically check generate SOW - let user decide
            self.rr_generate_check.setChecked(False)
        else:
            # Disable all RR controls when the checkbox is unchecked
            self.rr_generate_check.setChecked(False)
            self.rr_generate_check.setEnabled(False)

        # Update the engagement model
        self.engagement.include_rr = is_checked

    def on_rr_generate_check_changed(self, state):
        """
        Handle changes to the Generate R&R SOW checkbox.

        Args:
            state (int): The checkbox state
        """
        is_checked = state == Qt.Checked

        # Update the engagement model to control document generation
        self.engagement.generate_rr_sow = is_checked

    def update_email_platform(self, platform):
        """
        Update the email platform.

        Args:
            platform (str): The selected email platform
        """
        self.engagement.email_platform = platform

    def update_edr_monitoring(self, monitoring):
        """
        Update the EDR monitoring option.

        Args:
            monitoring (str): The selected EDR monitoring option
        """
        self.engagement.edr_monitoring = monitoring

    def update_endpoint_count(self, value):
        """
        Update the endpoint count.

        Args:
            value (int): The new endpoint count
        """
        self.engagement.endpoint_count = value

    def update_pricing_model(self, checked):
        """
        Update the pricing model.

        Args:
            checked (bool): Whether the radio button is checked
        """
        if checked:
            # Only update when a radio button is checked (not when unchecked)
            if self.sender() == self.fixed_fee_radio:
                self.engagement.is_fixed_fee = True
            elif self.sender() == self.time_materials_radio:
                self.engagement.is_fixed_fee = False

    def update_taci(self, state):
        """
        Update the TACI inclusion.

        Args:
            state (int): The checkbox state
        """
        self.engagement.include_taci = state == Qt.Checked

    def on_remote_clicked(self):
        """
        Handle the Remote Assistance radio button click.
        """
        print("Remote radio clicked in DFIR screen")
        print(f"Remote radio checked: {self.remote_radio.isChecked()}")
        print(f"Onsite radio checked: {self.onsite_radio.isChecked()}")

        # Update button states
        self.remote_radio.setChecked(True)
        self.onsite_radio.setChecked(False)

        print(f"After update - Remote radio checked: {self.remote_radio.isChecked()}")
        print(f"After update - Onsite radio checked: {self.onsite_radio.isChecked()}")

        # For remote assistance, disable the resource count field (always 1)
        self.resource_spin.setValue(1)
        self.resource_spin.setEnabled(False)
        self.resource_label.setEnabled(False)

        # Update the engagement model
        self.engagement.rr_is_remote = True
        self.engagement.onsite_resources_count = 1

    def on_onsite_clicked(self):
        """
        Handle the Onsite Assistance radio button click.
        """
        print("Onsite radio clicked in DFIR screen")
        print(f"Remote radio checked: {self.remote_radio.isChecked()}")
        print(f"Onsite radio checked: {self.onsite_radio.isChecked()}")

        # Update button states
        self.remote_radio.setChecked(False)
        self.onsite_radio.setChecked(True)

        print(f"After update - Remote radio checked: {self.remote_radio.isChecked()}")
        print(f"After update - Onsite radio checked: {self.onsite_radio.isChecked()}")

        # For onsite assistance, enable the resource count field
        print("Enabling resource count field")
        self.resource_spin.setEnabled(True)
        self.resource_label.setEnabled(True)

        # Make sure the resource count field is visible, focused, and interactive
        self.resource_spin.show()
        self.resource_spin.setFocus()
        self.resource_spin.setReadOnly(False)

        # Reset any style that might be affecting the spinner
        self.resource_spin.setStyleSheet("")

        # Print the current state of the spinner
        print(f"Resource spin enabled: {self.resource_spin.isEnabled()}")
        print(f"Resource spin read-only: {self.resource_spin.isReadOnly()}")
        print(f"Resource spin minimum: {self.resource_spin.minimum()}")
        print(f"Resource spin maximum: {self.resource_spin.maximum()}")
        print(f"Resource spin value: {self.resource_spin.value()}")

        # Update the engagement model
        self.engagement.rr_is_remote = False
        self.engagement.onsite_resources_count = self.resource_spin.value()

    # Removed unused method

    def update_onsite_resources(self, value):
        """
        Update the number of onsite resources.

        Args:
            value (int): The new number of onsite resources
        """
        self.engagement.onsite_resources_count = value

    # Removed unused methods

    def update_baa(self, state):
        """
        Update the BAA inclusion.

        Args:
            state (int): The checkbox state
        """
        is_checked = state == Qt.Checked
        self.engagement.client.needs_baa = is_checked
        print(f"DEBUG - BAA checkbox changed to: {is_checked}")
        print(f"DEBUG - Client needs_baa attribute now: {self.engagement.client.needs_baa}")

    def update_dpa(self, state):
        """
        Update the DPA inclusion.

        Args:
            state (int): The checkbox state
        """
        is_checked = state == Qt.Checked
        self.engagement.client.needs_dpa = is_checked
        print(f"DEBUG - DPA checkbox changed to: {is_checked}")
        print(f"DEBUG - Client needs_dpa attribute now: {self.engagement.client.needs_dpa}")

    def update_ransomware_flag(self, state):
        """
        Handle changes to the ransomware/network intrusion checkbox.

        Args:
            state (int): The checkbox state
        """
        self.engagement.is_ransomware_or_network_intrusion = state == Qt.Checked

    def on_back_clicked(self):
        """
        Handle the Back button click.
        """
        self.back_clicked.emit()

    def on_generate_clicked(self):
        """
        Handle the Generate Documents button click.
        """
        # Update the engagement object with the form values
        self.engagement.endpoint_count = self.endpoint_count_spin.value()
        self.engagement.is_fixed_fee = self.fixed_fee_radio.isChecked()
        self.engagement.email_platform = self.email_combo.currentText()
        self.engagement.edr_monitoring = self.edr_combo.currentText()
        self.engagement.include_taci = self.taci_check.isChecked()
        self.engagement.include_rr = self.rr_check.isChecked()
        self.engagement.generate_rr_sow = self.rr_generate_check.isChecked()

        # The RR assistance type and resource count are already updated in the on_remote_clicked and on_onsite_clicked methods
        # But we'll set them here again to be sure
        self.engagement.rr_is_remote = self.remote_radio.isChecked()
        self.engagement.onsite_resources_count = self.resource_spin.value() if not self.engagement.rr_is_remote else 1

        self.engagement.client.needs_dpa = self.dpa_check.isChecked()
        self.engagement.client.needs_baa = self.baa_check.isChecked()

        # Debug output for BAA and DPA flags
        print(f"DEBUG - On Generate - BAA checkbox is checked: {self.baa_check.isChecked()}")
        print(f"DEBUG - On Generate - DPA checkbox is checked: {self.dpa_check.isChecked()}")
        print(f"DEBUG - On Generate - Client needs_baa attribute: {self.engagement.client.needs_baa}")
        print(f"DEBUG - On Generate - Client needs_dpa attribute: {self.engagement.client.needs_dpa}")

        # Set the ransomware flag if this is a Beazley client
        if self.is_beazley_client:
            self.engagement.is_ransomware_or_network_intrusion = self.ransomware_check.isChecked()

        # Validate the data
        if not self.validate_data():
            return

        # Emit the generate_clicked signal
        self.generate_clicked.emit(self.engagement)

    def eventFilter(self, obj, event):
        """
        Filter events for the radio buttons.

        Args:
            obj: The object that triggered the event
            event: The event

        Returns:
            bool: True if the event was handled, False otherwise
        """
        from PySide6.QtCore import QEvent

        # Only process events for the radio buttons
        if obj in [self.remote_radio, self.onsite_radio]:
            # For mouse button press events, handle the radio button selection
            if event.type() == QEvent.MouseButtonPress:
                if obj == self.remote_radio:
                    # Set the remote radio button as checked
                    self.remote_radio.setChecked(True)
                    self.onsite_radio.setChecked(False)
                    self.on_remote_clicked()
                elif obj == self.onsite_radio:
                    # First, make sure the RR checkbox is checked
                    # This will enable the RR group and all its controls
                    self.rr_check.setChecked(True)
                    self.on_rr_check_changed(Qt.Checked)

                    # Now set the radio button states
                    self.remote_radio.setChecked(False)
                    self.onsite_radio.setChecked(True)

                    # Explicitly enable the resource spinner
                    self.resource_spin.setEnabled(True)
                    self.resource_label.setEnabled(True)

                    # Call the handler method
                    self.on_onsite_clicked()
                return True  # Event handled

        # Let the parent class handle the event
        return super().eventFilter(obj, event)

    def validate_data(self):
        """
        Validate the form data.

        Returns:
            bool: True if the data is valid, False otherwise
        """
        from PySide6.QtWidgets import QMessageBox

        # Check if client name is empty
        if not self.engagement.client.name:
            QMessageBox.warning(self, "Validation Error", "Client name is required.")
            return False

        # Check if client address is empty
        if not self.engagement.client.address:
            QMessageBox.warning(self, "Validation Error", "Client address is required.")
            return False

        # Check if endpoint count is valid
        if self.engagement.endpoint_count <= 0:
            QMessageBox.warning(self, "Validation Error", "Endpoint count must be greater than 0.")
            return False

        # Check if onsite resources count is valid when onsite assistance is selected
        if self.engagement.include_rr and not self.engagement.rr_is_remote and self.engagement.onsite_resources_count <= 0:
            QMessageBox.warning(self, "Validation Error", "Onsite resources count must be greater than 0.")
            return False

        # All validations passed
        return True


if __name__ == "__main__":
    app = QApplication(sys.argv)
    client = Client("Test Client", "123 Main St", "AIG", "Baker & Hostetler LLP")
    dfir_screen = DFIRScreen(client)
    dfir_screen.show()
    sys.exit(app.exec())
