#!/usr/bin/env python3
"""
Test script to verify version 1.1.4 update is working correctly.
"""

def test_version_update():
    """Test that version has been updated to 1.1.4"""
    print("PACE VERSION UPDATE TEST")
    print("=" * 40)
    
    try:
        # Test main.py version
        from main import __version__
        print(f"✓ Main application version: {__version__}")
        
        if __version__ == "1.1.4":
            print("✓ PASS: Version correctly updated to 1.1.4")
        else:
            print(f"✗ FAIL: Expected 1.1.4, got {__version__}")
            return False
        
        # Test that README files exist
        import os
        
        readme_md_exists = os.path.exists("README.md")
        readme_txt_exists = os.path.exists("README.txt")
        changelog_exists = os.path.exists("CHANGELOG.md")
        
        print(f"✓ README.md exists: {readme_md_exists}")
        print(f"✓ README.txt exists: {readme_txt_exists}")
        print(f"✓ CHANGELOG.md exists: {changelog_exists}")
        
        # Test that README.md contains v1.1.4
        if readme_md_exists:
            with open("README.md", "r", encoding="utf-8") as f:
                readme_content = f.read()
                if "v1.1.4" in readme_content:
                    print("✓ PASS: README.md contains v1.1.4")
                else:
                    print("✗ FAIL: README.md does not contain v1.1.4")
                    return False
        
        # Test that CHANGELOG.md contains v1.1.4 changes
        if changelog_exists:
            with open("CHANGELOG.md", "r", encoding="utf-8") as f:
                changelog_content = f.read()
                if "[1.1.4]" in changelog_content and "Performance Optimizations" in changelog_content:
                    print("✓ PASS: CHANGELOG.md contains v1.1.4 changes")
                else:
                    print("✗ FAIL: CHANGELOG.md missing v1.1.4 changes")
                    return False
        
        # Test that optimized imports still work
        try:
            from utils.document_generator import DocumentGenerator
            from models.dfir import DFIREngagement
            from models.client import Client
            print("✓ PASS: Optimized imports working correctly")
        except ImportError as e:
            print(f"✗ FAIL: Import error after optimization: {e}")
            return False
        
        # Test that new R&R functionality works
        try:
            client = Client()
            client.name = "Test Client"
            
            dfir = DFIREngagement(client)
            dfir.include_rr = True
            dfir.generate_rr_sow = False  # New attribute
            
            # Test that the new attribute exists and works
            if hasattr(dfir, 'generate_rr_sow'):
                print("✓ PASS: New generate_rr_sow attribute exists")
            else:
                print("✗ FAIL: generate_rr_sow attribute missing")
                return False
                
        except Exception as e:
            print(f"✗ FAIL: R&R functionality error: {e}")
            return False
        
        print("\n" + "=" * 40)
        print("✓ ALL TESTS PASSED - VERSION 1.1.4 UPDATE SUCCESSFUL")
        print("=" * 40)
        return True
        
    except Exception as e:
        print(f"✗ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_version_update()
    exit(0 if success else 1)
