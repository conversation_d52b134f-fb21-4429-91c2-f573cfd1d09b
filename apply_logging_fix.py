#!/usr/bin/env python3
"""
Apply logging fixes to replace print statements in key files.
This is a targeted fix for the most critical files.
"""

import os
import re

def replace_prints_with_logging(filepath):
    """Replace print statements with appropriate logging calls"""
    
    if not os.path.exists(filepath):
        print(f"File not found: {filepath}")
        return False
    
    print(f"Processing: {filepath}")
    
    with open(filepath, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    
    # Count original print statements
    original_prints = len(re.findall(r'\bprint\s*\(', content))
    
    # Replace different types of print statements with appropriate logging
    
    # Error prints
    content = re.sub(
        r'print\(f?"Error ([^"]*)"([^)]*)\)',
        r'logger.error(f"\1"\2)',
        content
    )
    
    # Warning prints  
    content = re.sub(
        r'print\(f?"Warning ([^"]*)"([^)]*)\)',
        r'logger.warning(f"\1"\2)',
        content
    )
    
    # Debug prints (DEBUG prefix or detailed info)
    content = re.sub(
        r'print\(f?"DEBUG ([^"]*)"([^)]*)\)',
        r'logger.debug(f"\1"\2)',
        content
    )
    
    # Success/completion prints
    content = re.sub(
        r'print\(f?"([^"]*successfully[^"]*)"([^)]*)\)',
        r'logger.info(f"\1"\2)',
        content
    )
    
    content = re.sub(
        r'print\(f?"([^"]*generated[^"]*)"([^)]*)\)',
        r'logger.info(f"\1"\2)',
        content
    )
    
    content = re.sub(
        r'print\(f?"([^"]*completed[^"]*)"([^)]*)\)',
        r'logger.info(f"\1"\2)',
        content
    )
    
    # General informational prints (most remaining ones)
    content = re.sub(
        r'print\(f?"([^"]*)"([^)]*)\)',
        r'logger.info(f"\1"\2)',
        content
    )
    
    # Handle non-f-string prints
    content = re.sub(
        r'print\("([^"]*)"([^)]*)\)',
        r'logger.info("\1"\2)',
        content
    )
    
    # Count remaining print statements
    remaining_prints = len(re.findall(r'\bprint\s*\(', content))
    
    if content != original_content:
        # Create backup
        backup_path = f"{filepath}.backup"
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(original_content)
        
        # Write updated content
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"  ✅ Updated {filepath}")
        print(f"     Original prints: {original_prints}")
        print(f"     Remaining prints: {remaining_prints}")
        print(f"     Converted: {original_prints - remaining_prints}")
        print(f"     Backup saved: {backup_path}")
        return True
    else:
        print(f"  ℹ️  No changes needed for {filepath}")
        return False

def main():
    """Apply logging fixes to key files"""
    
    print("APPLYING LOGGING FIXES TO KEY FILES")
    print("=" * 50)
    
    # Key files to fix (starting with the worst offenders)
    key_files = [
        'utils/document_generator.py',  # 143 prints
        'gui/dfir_screen.py',          # 24 prints  
        'main.py',                     # 23 prints
        'models/dfir.py',              # 14 prints
        'utils/docx_wrapper.py'        # 8 prints
    ]
    
    total_files_updated = 0
    
    for filepath in key_files:
        if replace_prints_with_logging(filepath):
            total_files_updated += 1
        print()
    
    print("=" * 50)
    print(f"LOGGING FIX SUMMARY")
    print(f"Files processed: {len(key_files)}")
    print(f"Files updated: {total_files_updated}")
    print(f"Backup files created for safety")
    print("=" * 50)
    
    if total_files_updated > 0:
        print("\n✅ LOGGING FIXES APPLIED!")
        print("📝 Next steps:")
        print("1. Test the application to ensure it still works")
        print("2. Check the log files for proper output")
        print("3. Remove backup files once confirmed working")
    else:
        print("\nℹ️  No files needed updating")

if __name__ == "__main__":
    main()
