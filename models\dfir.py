"""
DFIR engagement model for PACE application.
"""

import os
from models.engagement import Engagement
from data.carriers import get_carrier_rate, is_fixed_fee_carrier
from data.pricing_tables import (
    get_fixed_fee_price, get_edr_soc_fee, get_phase_hours,
    get_beazley_fixed_fee_price
)

class DFIREngagement(Engagement):
    """
    Class representing a DFIR engagement in the PACE application.
    """

    def __init__(self, client=None):
        """
        Initialize a new DFIREngagement object.

        Args:
            client (Client): The client for this engagement
        """
        super().__init__(client, "DFIR")
        self.endpoint_count = 0
        self.is_fixed_fee = False
        self.email_platform = "M365"  # M365, Google, Exchange, Hybrid, Other
        self.edr_monitoring = "Monitoring and Threat Hunting New Console"  # Monitoring and Threat Hunting New Console, Monitoring and Threat Hunting Existing Console, Threat Hunting Only, Monitoring by Client, None
        self.include_taci = False
        self.include_rr = False
        self.rr_is_remote = True
        self.onsite_resources_count = 1
        self.is_ransomware_or_network_intrusion = False

    def to_dict(self):
        """
        Convert the DFIR engagement data to a dictionary.

        Returns:
            dict: A dictionary containing the DFIR engagement data
        """
        data = super().to_dict()
        data.update({
            "endpoint_count": self.endpoint_count,
            "is_fixed_fee": self.is_fixed_fee,
            "email_platform": self.email_platform,
            "edr_monitoring": self.edr_monitoring,
            "include_taci": self.include_taci,
            "include_rr": self.include_rr,
            "rr_is_remote": self.rr_is_remote,
            "is_ransomware_or_network_intrusion": self.is_ransomware_or_network_intrusion
        })
        return data

    @classmethod
    def from_dict(cls, data, client=None):
        """
        Create a DFIREngagement object from a dictionary.

        Args:
            data (dict): A dictionary containing DFIR engagement data
            client (Client): The client for this engagement

        Returns:
            DFIREngagement: A new DFIREngagement object
        """
        engagement = super().from_dict(data, client)

        engagement.endpoint_count = data.get("endpoint_count", 0)
        engagement.is_fixed_fee = data.get("is_fixed_fee", False)
        engagement.email_platform = data.get("email_platform", "M365")
        engagement.edr_monitoring = data.get("edr_monitoring", "Monitoring and Threat Hunting New Console")
        engagement.include_taci = data.get("include_taci", False)
        engagement.include_rr = data.get("include_rr", False)
        engagement.rr_is_remote = data.get("rr_is_remote", True)
        engagement.is_ransomware_or_network_intrusion = data.get("is_ransomware_or_network_intrusion", False)

        return engagement

    def is_valid(self):
        """
        Check if the DFIR engagement data is valid.

        Returns:
            bool: True if the DFIR engagement data is valid, False otherwise
        """
        return (super().is_valid() and
                self.endpoint_count > 0 and
                self.email_platform in ["M365", "Google", "Exchange", "Hybrid", "Other"] and
                self.edr_monitoring in ["Monitoring and Threat Hunting New Console", "Monitoring and Threat Hunting Existing Console",
                                       "Threat Hunting Only", "Monitoring by Client", "None"])

    def get_document_folder_name(self):
        """
        Get the name of the folder where documents for this engagement should be saved.

        Returns:
            str: The folder name
        """
        if not self.client or not self.client.name:
            return "Unnamed Client - Scoping Documents"

        return f"{self.client.name} - Scoping Documents"

    def get_sow_filename(self):
        """
        Get the filename for the SOW document.

        Returns:
            str: The filename for the SOW document
        """
        from datetime import datetime

        if not self.client or not self.client.name:
            return f"Unnamed Client IR Investigation - SOW {datetime.now().strftime('%Y%m%d')}.docx"

        return f"{self.client.name} IR Investigation - SOW {datetime.now().strftime('%Y%m%d')}.docx"

    def get_sow_template_path(self):
        """
        Get the path to the SOW template for this engagement.

        Returns:
            str: The path to the SOW template
        """
        # Base path for DFIR templates
        base_path = "templates/base_templates/dfir"

        # Special case for Beckage law firm - always use the Beckage template regardless of endpoint count
        if self.client and self.client.law_firm == "The Beckage Firm":
            return f"{base_path}/SOW_IR_Template_IR Investigation_Beckage_Template.docx"

        # Special case for Beazley carrier - always use the Beazley template regardless of endpoint count
        if self.client and self.client.insurance_carrier == "Beazley":
            return f"{base_path}/SOW__IR_template_Beazley_FFP.docx"

        # Special case for Chubb carrier - always use Chubb templates regardless of endpoint count
        if self.client and self.client.insurance_carrier == "Chubb":
            # Chubb doesn't support fixed fee pricing, always use the standard template
            return f"{base_path}/SOW_chubb_IR_cs_codes_template.docx"

        # For Coalition and AXA XL carriers, check endpoint count
        if self.client and self.client.insurance_carrier in ["Coalition", "AXA XL"]:
            # For engagements with more than 2,000 endpoints, use hourly (single price) SOW
            if self.endpoint_count > 2000:
                return f"{base_path}/SOW_IR_Template_single_price_IR_Investigation.docx"
            else:
                # For 2,000 or fewer endpoints, use fixed fee template
                return f"{base_path}/SOW_Fixed_Fee_IR_template.docx"

        # For other carriers with fixed fee explicitly selected
        if self.is_fixed_fee:
            # For engagements with more than 2,000 endpoints, use hourly (single price) SOW
            if self.endpoint_count > 2000:
                return f"{base_path}/SOW_IR_Template_single_price_IR_Investigation.docx"
            else:
                # For 2,000 or fewer endpoints, use fixed fee template
                return f"{base_path}/SOW_Fixed_Fee_IR_template.docx"

        # Default template for all Time & Materials engagements (single price)
        return f"{base_path}/SOW_IR_Template_single_price_IR_Investigation.docx"

    def get_msa_filename(self):
        """
        Get the filename for the MSA document.

        Returns:
            str: The filename for the MSA document
        """
        from datetime import datetime

        if not self.client or not self.client.name:
            return f"Unnamed Client Master Services Agreement (MSA) {datetime.now().strftime('%Y%m%d')}.docx"

        return f"{self.client.name} Master Services Agreement (MSA) {datetime.now().strftime('%Y%m%d')}.docx"

    def get_msa_template_path(self):
        """
        Get the path to the MSA template for this engagement.

        Returns:
            str: The path to the MSA template
        """
        if self.client and self.client.law_firm:
            # Check for law firm-specific MSA templates
            # Special case for Baker & Hostetler LLP with TASB
            if self.client.law_firm == "Baker & Hostetler LLP" and hasattr(self.client, 'is_tasb') and self.client.is_tasb:
                return "templates/base_templates/msa_templates/Baker_&_Hostetler_LLP_Beazley_TASB_Master_Services_Agreement_Template.docx"
            # Special case for Jackson Lewis PC with BAA
            elif self.client.law_firm == "Jackson Lewis PC" and self.needs_baa:
                return "templates/base_templates/msa_templates/Jackson_Lewis_PC_with_BAA_Master_Services_Agreement_Template.docx"
            # Standard law firm templates
            elif self.client.law_firm == "Baker & Hostetler LLP":
                return "templates/base_templates/msa_templates/Baker_&_Hostetler_LLP_(2-Party)_Master_Services_Agreement_Template.docx"
            elif self.client.law_firm == "Baker, Donelson, Bearman, Caldwell & Berkowitz P.C":
                return "templates/base_templates/msa_templates/Baker_Donelson_Bearman_Caldwell_&_Berkowitz_P.C._Master_Services_Agreement_Template.docx"
            elif self.client.law_firm == "Cipriani & Werner PC":
                return "templates/base_templates/msa_templates/Cipriani_&_Werner_PC_Master_Services_Agreement_Template.docx"
            elif self.client.law_firm == "Clark Hill PLC":
                return "templates/base_templates/msa_templates/Clark_Hill_PLC_Master_Services_Agreement.docx"
            elif self.client.law_firm == "Constangy, Brooks, Smith & Prophete, LLP":
                return "templates/base_templates/msa_templates/Constangy_Brooks_Smith_&_Prophete_LLP_Master_Services_Agreement_Template.docx"
            elif self.client.law_firm == "Dykema Gossett PLLC":
                return "templates/base_templates/msa_templates/Dykema_Gossett_PLLC_Master_Service_Agreement_Template.docx"
            elif self.client.law_firm == "Eckert Seamans Cherin & Mellott, LLC":
                return "templates/base_templates/msa_templates/Eckert_Seamans_Cherin_&_Mellott_LLC_Master_Service_Agreement_Template.docx"
            elif self.client.law_firm == "Gordon Rees Scully Mansukhani, LLP":
                return "templates/base_templates/msa_templates/Gordon_Rees_Scully_Mansukhani_LLP_Master_Services_Agreement_Template.docx"
            elif self.client.law_firm == "Greenberg Traurig, LLP":
                return "templates/base_templates/msa_templates/Greenberg_Traurig_LLP_Master_Services_Agreement_Template.docx"
            elif self.client.law_firm == "Jackson Lewis PC":
                return "templates/base_templates/msa_templates/Jackson_Lewis_PC_Master_Services_Agreement_Template.docx"
            elif self.client.law_firm == "Lewis Brisbois Bisgaard & Smith, LLP":
                return "templates/base_templates/msa_templates/Lewis_Brisbois_Bisgaard_&_Smith_LLP_Master_Services_Agreement_Template.docx"
            elif self.client.law_firm == "Locke Lord":
                return "templates/base_templates/msa_templates/Locke_Lord_Master_Services_Agreement_Template.docx"
            elif self.client.law_firm == "Maynard Nexsen PC":
                return "templates/base_templates/msa_templates/Maynard_Nexsen_PC_Master_Services_Agreement_Template.docx"
            elif self.client.law_firm == "McDonald Hopkins LLC":
                return "templates/base_templates/msa_templates/McDonald_Hopkins_LLC_Master_Services_Agreement_Template.docx"
            elif self.client.law_firm == "Mullen Coughlin LLC":
                return "templates/base_templates/msa_templates/Mullen_Coughlin_LLC_Master_Services_Agreement_Template.docx"
            elif self.client.law_firm == "Nelson Mullins Riley & Scarborough LLP":
                return "templates/base_templates/msa_templates/Nelson_Mullins_Riley_&_Scarborough_LLP_Master_Services_Agreement_Template.docx"
            elif self.client.law_firm == "Norton Rose Fulbright":
                return "templates/base_templates/msa_templates/Norton_Rose_Fulbright_Master_Services_Agreement_Template.docx"
            elif self.client.law_firm == "Octillo Law PLLC":
                return "templates/base_templates/msa_templates/Octillo_Law_PLLC_Master_Services_Agreement_Template.docx"
            elif self.client.law_firm == "Ogletree Deakins":
                return "templates/base_templates/msa_templates/Ogletree_Deakins_Master_Services_Agreement_Template.docx"
            elif self.client.law_firm == "Ruskin Moscou Faltischek, P.C.":
                return "templates/base_templates/msa_templates/Ruskin_Moscou_Faltischek_P.C._Master_Services_Agreement_Template.docx"
            elif self.client.law_firm == "Shook, Hardy & Bacon L.L.P.":
                return "templates/base_templates/msa_templates/Shook_Hardy_&_Bacon_LLP_Master_Services_Agreement_Template.docx"
            elif self.client.law_firm == "The Beckage Firm PLLC" or self.client.law_firm == "The Beckage Firm":
                return "templates/base_templates/msa_templates/The_Beckage_Firm_PLLC_Master_Services_Agreement_Template.docx"
            elif self.client.law_firm == "Woods Rogers":
                return "templates/base_templates/msa_templates/Woods_Rogers_Master_Services_Agreement_Template.docx"
            else:
                # Generic 3-party MSA for any other law firm
                return "templates/base_templates/msa_templates/IR_Services_3_Party_Master_Services_Agreement_Template.docx"

        # Default 2-party MSA template (no law firm)
        return "templates/base_templates/msa_templates/IR_Services_2_Party_Master_Services_Agreement_Template.docx"

    def get_baa_filename(self):
        """
        Get the filename for the BAA document.

        Returns:
            str: The filename for the BAA document
        """
        from datetime import datetime

        if not self.client or not self.client.name:
            return f"Unnamed Client Business Associate Agreement (BAA) {datetime.now().strftime('%Y%m%d')}.docx"

        return f"{self.client.name} Business Associate Agreement (BAA) {datetime.now().strftime('%Y%m%d')}.docx"

    def get_baa_template_path(self):
        """
        Get the path to the BAA template for this engagement.

        Returns:
            str: The path to the BAA template
        """
        # Use the standard BAA template with spaces (same format as DPA)
        return "templates/base_templates/baa/Template_Business_Associate_Agreement.docx"

    def get_dpa_filename(self):
        """
        Get the filename for the DPA document.

        Returns:
            str: The filename for the DPA document
        """
        from datetime import datetime

        if not self.client or not self.client.name:
            return f"Unnamed Client Data Processing Agreement (DPA) {datetime.now().strftime('%Y%m%d')}.docx"

        return f"{self.client.name} Data Processing Agreement (DPA) {datetime.now().strftime('%Y%m%d')}.docx"

    def get_dpa_template_path(self):
        """
        Get the path to the DPA template for this engagement.

        Returns:
            str: The path to the DPA template
        """
        # Use the original DPA template with spaces (same format as BAA)
        return "templates/base_templates/dpa/Template_Data_Processing_Agreement.docx"

    def get_taci_sow_filename(self):
        """
        Get the filename for the TACI SOW document.

        Returns:
            str: The filename for the TACI SOW document
        """
        from datetime import datetime

        if not self.client or not self.client.name:
            return f"Unnamed Client Ransom Communications - SOW {datetime.now().strftime('%Y%m%d')}.docx"

        return f"{self.client.name} Ransom Communications - SOW {datetime.now().strftime('%Y%m%d')}.docx"

    def get_taci_sow_template_path(self):
        """
        Get the path to the TACI SOW template for this engagement.

        Returns:
            str: The path to the TACI SOW template
        """
        # Special case for Beckage law firm
        if self.client and self.client.law_firm == "The Beckage Firm":
            return "templates/base_templates/taci/SOW_RN_Template_Beckage_Firm_Ransom_Consulting.docx"

        # Special case for Chubb carrier
        if self.client and self.client.insurance_carrier == "Chubb":
            return "templates/base_templates/taci/SOW_RN_Template_Chubb_Only_Ransom_Consulting.docx"

        # Special case for Coalition carrier (fixed fee)
        if self.client and self.client.insurance_carrier == "Coalition":
            return "templates/base_templates/taci/SOW_Coalition_Ransom_Consulting_FFP_template.docx"

        # Default template for all other carriers
        return "templates/base_templates/taci/SOW_Ransom_Consulting_template.docx"

    def get_rr_sow_filename(self):
        """
        Get the filename for the RR SOW document.

        Returns:
            str: The filename for the RR SOW document
        """
        from datetime import datetime

        if not self.client or not self.client.name:
            return f"Unnamed Client Recovery & Remediation - SOW {datetime.now().strftime('%Y%m%d')}.docx"

        return f"{self.client.name} Recovery & Remediation - SOW {datetime.now().strftime('%Y%m%d')}.docx"

    def get_rr_sow_template_path(self):
        """
        Get the path to the RR SOW template for this engagement.

        Returns:
            str: The path to the RR SOW template
        """
        # Special case for Beazley
        if self.client and self.client.insurance_carrier == "Beazley":
            return "templates/base_templates/rr/SOW_ON_Template_Beazley_Only_Recovery_&_Restoration_Support.docx"

        # Standard template for all other carriers
        return "templates/base_templates/rr/SOW_ON_Template_Recovery_&_Restoration_Support.docx"

    def get_rate(self):
        """
        Get the hourly rate for this engagement.

        Returns:
            str: The hourly rate formatted as a string with $ sign
        """
        from data.carriers import get_carrier_rate

        # Default rate
        rate = 325

        # Get rate from carrier if available
        if self.client and self.client.insurance_carrier:
            rate = get_carrier_rate(self.client.insurance_carrier, "DFIR")

        return f"${int(rate)}"

    def get_taci_rate(self):
        """
        Get the hourly rate for TACI services.

        Returns:
            str: The hourly rate formatted as a string with $ sign
        """
        from data.carriers import get_carrier_rate

        # Default rate
        rate = 375

        # Get rate from carrier if available
        if self.client and self.client.insurance_carrier:
            rate = get_carrier_rate(self.client.insurance_carrier, "TACI")

        return f"${int(rate)}"

    def get_placeholders(self):
        """
        Get the placeholders for document generation.

        Returns:
            dict: A dictionary of placeholders and their values
        """
        from datetime import datetime

        # Calculate pricing based on the engagement type and client
        try:
            # For Chubb carrier, we need to calculate the pricing directly
            if self.client and self.client.insurance_carrier == "Chubb":
                pricing = {}
                pricing = self._calculate_chubb_pricing(pricing)
            else:
                pricing = self.calculate_pricing()

            if pricing is None:
                print("Warning: Pricing calculation returned None, using default pricing")
                # This shouldn't happen with our improved calculate_pricing method
                # but we'll handle it just in case
                pricing = {}
        except Exception as e:
            print(f"Error calculating pricing: {e}")
            import traceback
            traceback.print_exc()
            pricing = {}

        # Extract hours and costs from the pricing dictionary
        # Use get() with default values to avoid NoneType errors
        total_hours = pricing.get('sp_total_hours', 0)
        phase1_hours = pricing.get('phase1_sp_hours', 0)
        phase2_hours = pricing.get('phase2_sp_hours', 0)
        phase3_hours = pricing.get('phase3_sp_hours', 0)
        phase4_hours = pricing.get('phase4_sp_hours', 0)
        phase5_hours = pricing.get('phase5_sp_hours', 0)
        phase6_hours = pricing.get('phase6_sp_hours', 0)

        # Get costs from the pricing dictionary
        hourly_rate = pricing.get('dfir_rate', 325)  # Default hourly rate

        # Get phase costs from the pricing dictionary
        # Convert string values to integers for calculations
        phase1_costs_str = pricing.get('phase1_sp_costs', '$0')
        phase2_costs_str = pricing.get('phase2_sp_costs', '$0')
        phase3_costs_str = pricing.get('phase3_sp_costs', '$0')
        phase4_costs_str = pricing.get('phase4_sp_costs', '$0')
        phase5_costs_str = pricing.get('phase5_sp_costs', '$0')
        phase6_costs_str = pricing.get('phase6_sp_costs', '$0')
        total_costs_str = pricing.get('sp_costs', '$0')

        # Convert string values to integers for calculations
        try:
            phase1_costs = int(phase1_costs_str.replace('$', '').replace(',', '')) if isinstance(phase1_costs_str, str) else 0
        except (ValueError, AttributeError):
            phase1_costs = 0

        try:
            phase2_costs = int(phase2_costs_str.replace('$', '').replace(',', '')) if isinstance(phase2_costs_str, str) else 0
        except (ValueError, AttributeError):
            phase2_costs = 0

        try:
            phase3_costs = int(phase3_costs_str.replace('$', '').replace(',', '')) if isinstance(phase3_costs_str, str) else 0
        except (ValueError, AttributeError):
            phase3_costs = 0

        try:
            phase4_costs = int(phase4_costs_str.replace('$', '').replace(',', '')) if isinstance(phase4_costs_str, str) else 0
        except (ValueError, AttributeError):
            phase4_costs = 0

        try:
            phase5_costs = int(phase5_costs_str.replace('$', '').replace(',', '')) if isinstance(phase5_costs_str, str) else 0
        except (ValueError, AttributeError):
            phase5_costs = 0

        try:
            phase6_costs = int(phase6_costs_str.replace('$', '').replace(',', '')) if isinstance(phase6_costs_str, str) else 0
        except (ValueError, AttributeError):
            phase6_costs = 0

        try:
            total_costs = int(total_costs_str.replace('$', '').replace(',', '')) if isinstance(total_costs_str, str) else 0
        except (ValueError, AttributeError):
            total_costs = 0

        # Calculate EDR monitoring fee
        try:
            edr_fee = 0
            if self.endpoint_count <= 100:
                edr_fee = 2500
            elif self.endpoint_count <= 2000:
                edr_fee = 5000
            elif self.endpoint_count <= 5000:
                edr_fee = 11200
            elif self.endpoint_count <= 10000:
                edr_fee = 22500
            elif self.endpoint_count <= 20000:
                edr_fee = 30000
            else:
                edr_fee = 30000  # Default for larger environments

            # Only apply EDR fee if monitoring is selected
            # Check if the EDR monitoring option contains both 'Monitoring' and 'Threat Hunting'
            if not ("Monitoring" in self.edr_monitoring and "Threat Hunting" in self.edr_monitoring):
                edr_fee = 0
        except Exception as e:
            print(f"Error calculating EDR fee: {e}")
            edr_fee = 0

        # Import RichText for handling ampersands
        try:
            from docxtpl import RichText
            richtext_available = True
            # Print debug information
            if self.client and self.client.law_firm and '&' in self.client.law_firm:
                print(f"Law firm name contains ampersand: {self.client.law_firm}")
                print(f"Using RichText for law firm name to preserve ampersand")
        except ImportError:
            # If RichText is not available, use regular strings
            print("Warning: RichText not available, ampersands may not be preserved")
            richtext_available = False
            RichText = str  # Fallback to string

        # For Greenberg Traurig MSA, use plain strings instead of RichText to preserve formatting
        msa_template_path = self.get_msa_template_path() if hasattr(self, 'get_msa_template_path') else ""
        use_richtext = richtext_available and 'Greenberg' not in msa_template_path

        if use_richtext:
            # Use RichText for templates that support it
            client_name_rt = RichText(self.client.name) if self.client and self.client.name else RichText("")
            client_address_rt = RichText(self.client.address) if self.client and self.client.address else RichText("")
            law_firm_rt = RichText(self.client.law_firm) if self.client and self.client.law_firm else RichText("")
        else:
            # Use plain strings for Greenberg templates or when RichText is not available
            client_name_rt = self.client.name if self.client and self.client.name else ""
            client_address_rt = self.client.address if self.client and self.client.address else ""
            law_firm_rt = self.client.law_firm if self.client and self.client.law_firm else ""

        placeholders = {
            # Client information
            "client": client_name_rt,
            "Client": client_name_rt,  # Capitalized version
            "client_name": client_name_rt,  # Alternative name
            "company": client_name_rt,  # Alternative name
            "client_address": client_address_rt,
            "address": client_address_rt,  # Alternative name
            "date": datetime.now().strftime("%B %d, %Y"),
            "current_date": datetime.now().strftime("%B %d, %Y"),  # Alternative name
            "law_firm": law_firm_rt,
            "Lawfirm": law_firm_rt,  # Capitalized version
            "lawfirm": law_firm_rt,  # Lowercase version for MSA template
            "firm": law_firm_rt,  # Alternative name
            "counsel": law_firm_rt,  # Alternative name
            "CLIENT": client_name_rt,  # For specific law firm MSA templates
            "CLIENT_NAME": client_name_rt,  # For Baker Donelson MSA template


            # Rates and counts
            "dfir_rate": f"${int(hourly_rate)}",
            "sp_endpoint_count": f"{self.endpoint_count:,}",
            "endpoint_count": f"{self.endpoint_count:,}",  # Alternative name
            "monitoring_period": "30",  # Default period
            "monitoring_cost": "25",  # Default cost per endpoint
            "monitoring_type": self.edr_monitoring,
            "edr_monitoring": self.edr_monitoring,
            "pricing_model": "Fixed Fee" if self.is_fixed_fee else "Single Price",  # Changed from "Time & Materials" to "Single Price"

            # Phase hours
            "sp_total_hours": str(total_hours),
            "total_hours": str(total_hours),  # Alternative name

            # Phase 1 = CS410 (Project Management)
            "phase1_sp_hours": str(phase1_hours),
            "phase1_hours": str(phase1_hours),  # Add non-sp version
            "cs410_hours": str(phase1_hours),  # CS410 for Chubb template

            # Phase 2 = CS120 (Digital Forensic Analysis)
            "phase2_sp_hours": str(phase2_hours),  # Add sp version
            "phase2_hours": str(phase2_hours),  # Add non-sp version
            "cs120_forensic_hours": str(phase2_hours),  # CS120 Forensic for Chubb template

            # Phase 3 = Part of CS120 Email (Email Tenant Analysis)
            "phase3_sp_hours": str(phase3_hours),  # Add sp version
            "phase3_hours": str(phase3_hours),  # Standard name for Chubb template

            # Phase 4 = Part of CS120 Email (Log Analysis)
            "phase4_sp_hours": str(phase4_hours),
            "phase4_hours": str(phase4_hours),  # Standard name for Chubb template

            # CS120 Email combines Phase 3 (Email Tenant) and Phase 4 (Log Analysis)
            "cs120_email_hours": str(int(phase3_hours) + int(phase4_hours)),  # CS120 Email for Chubb template

            # Phase 5 = CS210 (Endpoint Monitoring)
            "phase5_sp_hours": str(phase5_hours),
            "phase5_hours": str(phase5_hours),  # Add non-sp version
            "cs210_hours": str(phase5_hours),  # CS210 for Chubb template

            # Phase 6 = CS430 (Final Report)
            "phase6_sp_hours": str(phase6_hours),
            "phase6_hours": str(phase6_hours),  # Add non-sp version
            "cs430_hours": str(phase6_hours),  # CS430 for Chubb template

            # Phase costs
            "phase1_sp_costs": f"${phase1_costs:,}",
            "phase1_costs": f"${phase1_costs:,}",  # Add non-sp version
            "phase2_sp_costs": f"${phase2_costs:,}",
            "phase2_costs": f"${phase2_costs:,}",  # Add non-sp version
            "phase3_sp_costs": f"${phase3_costs:,}",
            "phase3_costs": f"${phase3_costs:,}",  # Add non-sp version
            "phase4_sp_costs": f"${phase4_costs:,}",
            "phase4_costs": f"${phase4_costs:,}",  # Add non-sp version
            "phase5_sp_costs": f"${phase5_costs:,}",
            "phase5_costs": f"${phase5_costs:,}",  # Add non-sp version
            "phase6_sp_costs": f"${phase6_costs:,}",
            "phase6_costs": f"${phase6_costs:,}",  # Add non-sp version

            # Total costs
            "sp_costs": f"${total_costs:,}",
            "labor_costs": f"${total_costs:,}",  # Alternative name
            "total_labor_costs": f"${total_costs:,}",  # Standard name for Chubb template
            "Estimated Labor Costs": f"${total_costs:,}",  # Capitalized version
            "sp_edr_fee": f"${edr_fee:,}",
            "edr_fee": f"${edr_fee:,}",  # Alternative name
            "EDR SOC Monitoring Fee": f"${edr_fee:,}",  # Capitalized version
            "sp_total_costs": f"${total_costs + edr_fee:,}",
            "total_costs": f"${total_costs + edr_fee:,}",  # Alternative name
            "Total Estimated Costs": f"${total_costs + edr_fee:,}",  # Capitalized version
            "Estimated Labor Hours": f"{total_hours}",  # Capitalized version for total hours
            "formatted_date": datetime.now().strftime("%B %d, %Y"),  # Formatted date for Chubb template

            # Fixed Fee Pricing placeholders
            "ffp_endpoint_count": f"{self.endpoint_count:,}",
            "endpoint_count": f"{self.endpoint_count:,}",  # Duplicate to ensure both placeholders work
            "ffp_total_cost": f"${self._get_fixed_fee_price():,}",
            "ffp_soc_fee": f"${edr_fee:,}",
            "ffp_all_costs": f"${self._get_fixed_fee_price() + edr_fee:,}",

            # Email platform
            "email_platform": self.email_platform,

            # Flags for conditional sections
            "is_fixed_fee": "true" if self.is_fixed_fee else "false",
            "is_time_materials": "true" if not self.is_fixed_fee else "false"
        }

        # Add Beazley-specific placeholders if this is a Beazley engagement
        if self.client and self.client.insurance_carrier == "Beazley":
            from data.pricing_tables import get_beazley_fixed_fee_price, get_edr_soc_fee

            # Add Beazley fixed fee pricing placeholders
            placeholders["beazley_ffp_endpoint_count"] = f"{self.endpoint_count:,}"

            # Get Beazley fixed fee price with error handling
            try:
                beazley_ffp_est_cost = get_beazley_fixed_fee_price(self.endpoint_count, not getattr(self, 'rr_is_remote', False))
                if beazley_ffp_est_cost is None:
                    beazley_ffp_est_cost = 0
                placeholders["beazley_ffp_est_cost"] = f"${beazley_ffp_est_cost:,}"
            except Exception as e:
                print(f"Error getting Beazley fixed fee price: {e}")
                beazley_ffp_est_cost = 0
                placeholders["beazley_ffp_est_cost"] = "$0"

            # Calculate EDR SOC monitoring fee if applicable
            edr_fee = 0
            if self.edr_monitoring in ["Monitoring and Threat Hunting", "Monitoring and Threat Hunting New Console"]:
                try:
                    edr_fee = get_edr_soc_fee(self.endpoint_count)
                    if edr_fee is None:
                        edr_fee = 0
                except Exception as e:
                    print(f"Error getting EDR SOC fee: {e}")
                    edr_fee = 0

            placeholders["beazley_edr_fee"] = f"${edr_fee:,}"

            # Calculate total costs
            placeholders["beazley_total_costs"] = f"${beazley_ffp_est_cost + edr_fee:,}"

        # Add Recovery and Remediation (RR) placeholders if RR is included
        if self.include_rr:
            # Calculate RR hours based on remote/onsite and resource count
            rr_hourly_rate = 275.0  # Default rate
            if self.client and self.client.insurance_carrier:
                from data.carriers import get_carrier_rate
                rr_hourly_rate = get_carrier_rate(self.client.insurance_carrier, "Recovery and Remediation")

            # Calculate hours based on remote/onsite and resource count
            if self.rr_is_remote:
                # For remote support: 150 hours total (updated from 100)
                rr_total_hours = 150
            else:
                # For onsite support: 200 hours for first resource, 150 for each additional
                if self.onsite_resources_count == 1:
                    rr_total_hours = 200
                else:
                    # First resource gets 200 hours, additional resources get 150 each
                    rr_total_hours = 200 + (150 * (self.onsite_resources_count - 1))

                # Special case for Beazley
                if self.client and self.client.insurance_carrier == "Beazley":
                    # For Beazley with onsite support: 150 hours for first resource, 100 for each additional
                    if self.onsite_resources_count == 1:
                        rr_total_hours = 150
                    else:
                        # First resource gets 150 hours, additional resources get 100 each
                        rr_total_hours = 150 + (100 * (self.onsite_resources_count - 1))
                    
                    # Store the additional 50 hours that would go to Phase 2 of IR SOW
                    placeholders["beazley_ir_phase2_additional_hours"] = 50 * self.onsite_resources_count
                    # Override the total hours calculation for Beazley
                    rr_total_hours = 150 * self.onsite_resources_count

            rr_total_cost = int(rr_hourly_rate * rr_total_hours)

            # Add RR placeholders
            placeholders["rr_hourly_rate"] = str(int(rr_hourly_rate))
            placeholders["rr_rate"] = str(int(rr_hourly_rate))
            placeholders["rr_total_hours"] = str(rr_total_hours)
            placeholders["rr_total_cost"] = f"${rr_total_cost:,}"
            placeholders["rr_type"] = "Remote" if self.rr_is_remote else "Onsite"
            placeholders["rr_resources_count"] = str(self.onsite_resources_count)

            # Add Beazley-specific RR placeholders
            placeholders["beazley_rr_hours"] = str(rr_total_hours)
            placeholders["beazley_rr_total_cost"] = f"${rr_total_cost:,}"

        # Add TACI placeholders for TACI SOW generation
        taci_hourly_rate = 375.0  # Default rate
        if self.client and self.client.insurance_carrier:
            from data.carriers import get_carrier_rate
            taci_hourly_rate = get_carrier_rate(self.client.insurance_carrier, "TACI")

        # Standard TACI pricing - convert to single price model
        phase1_hours = 40  
        phase2_hours = 25  

        # Add TACI placeholders
        placeholders["taci_rate"] = f"${int(taci_hourly_rate)}"
        placeholders["taci_phase1_costs"] = f"${int(taci_hourly_rate * phase1_hours):,}"
        placeholders["taci_phase2_costs"] = f"${int(taci_hourly_rate * phase2_hours):,}"
        placeholders["taci_total_cost"] = f"${int(taci_hourly_rate * (phase1_hours + phase2_hours)):,}"

        # Add Beckage-specific TACI placeholders
        placeholders["beckage_taci_phase1_costs"] = f"${int(taci_hourly_rate * phase1_hours):,}"

        # For Chubb carrier, we need to calculate the CS code placeholders directly
        if self.client and self.client.insurance_carrier == "Chubb":
            from data.carriers import get_carrier_rate
            from data.pricing_tables import get_phase_hours, get_edr_soc_fee

            # Get the hourly rate for Chubb
            dfir_rate = get_carrier_rate("Chubb", "DFIR")

            # Add the endpoint count placeholder
            placeholders["chubb_endpoint_count"] = f"{self.endpoint_count:,}"
            placeholders["endpoint_count"] = f"{self.endpoint_count:,}"  # Add this for templates that use endpoint_count

            # CS120 – Digital Forensic Analysis (Phase 2)
            phase2_hours = get_phase_hours(2, self.endpoint_count)
            phase2_costs = int(float(dfir_rate) * phase2_hours)
            placeholders["cs120_forensic_hours"] = str(phase2_hours)
            placeholders["cs120_forensic_costs"] = f"${phase2_costs:,}"

            # CS120 – Email Tenant Analysis (Phase 3) and Log Analysis (Phase 4)
            phase3_base_hours = get_phase_hours(3, self.endpoint_count)
            phase4_base_hours = get_phase_hours(4, self.endpoint_count)

            if self.email_platform == "Exchange":
                phase3_hours = phase3_base_hours
                phase4_hours = phase4_base_hours + 10
            elif self.email_platform == "Hybrid":
                phase3_hours = phase3_base_hours
                phase4_hours = phase4_base_hours + 15
            else:
                phase3_hours = phase3_base_hours
                phase4_hours = phase4_base_hours

            # Combine Phase 3 and Phase 4 hours for CS120 Email
            combined_hours = phase3_hours + phase4_hours
            combined_costs = int(float(dfir_rate) * combined_hours)
            placeholders["cs120_email_hours"] = str(combined_hours)
            placeholders["cs120_email_costs"] = f"${combined_costs:,}"

            # CS210 – Endpoint Monitoring & Threat Hunting Analysis (Phase 5)
            if self.edr_monitoring != "None":
                phase4_hours = get_phase_hours(5, self.endpoint_count)
                phase4_costs = int(float(dfir_rate) * phase4_hours)
                placeholders["cs210_hours"] = str(phase4_hours)
                placeholders["cs210_costs"] = f"${phase4_costs:,}"

                if self.edr_monitoring in ["Monitoring and Threat Hunting", "Monitoring and Threat Hunting New Console"]:
                    edr_fee = get_edr_soc_fee(self.endpoint_count)
                else:
                    edr_fee = 0
            else:
                phase4_hours = 0
                phase4_costs = 0
                edr_fee = 0
                placeholders["cs210_hours"] = "0"
                placeholders["cs210_costs"] = "$0"

            # CS430 – Final Report (Phase 6)
            report_hours = 15
            report_cost = int(float(dfir_rate) * report_hours)
            placeholders["cs430_hours"] = str(report_hours)
            placeholders["cs430_costs"] = f"${report_cost:,}"

            # Calculate subtotal for phases 2-6
            subtotal_hours = phase2_hours + combined_hours + report_hours

            # CS410 - Project Management, Planning & Reporting (Phase 1)
            # Use endpoint-based hours instead of percentage calculation
            phase1_hours = get_phase_hours(1, self.endpoint_count)
            phase1_cost = int(float(dfir_rate) * phase1_hours)
            placeholders["cs410_hours"] = str(phase1_hours)
            placeholders["cs410_costs"] = f"${phase1_cost:,}"

            # Calculate totals
            total_hours = phase1_hours + phase2_hours + phase3_hours + phase4_hours + report_hours
            total_labor_costs = phase1_cost + phase2_costs + phase3_costs + phase4_costs + report_cost

            # Add the mandatory $599 fee for Chubb
            mandatory_fee = 599
            placeholders["mandatory_fee"] = f"${mandatory_fee:,}"
            placeholders["chubb_mandatory_fee"] = f"${mandatory_fee:,}"

            # Add the mandatory fee to the total costs
            total_costs = total_labor_costs + edr_fee + mandatory_fee

            # Add total placeholders for CS code template
            placeholders["total_hours"] = str(total_hours)
            placeholders["total_labor_costs"] = f"${total_labor_costs:,}"
            placeholders["edr_fee"] = f"${edr_fee:,}"
            placeholders["total_costs"] = f"${total_costs:,}"

        return placeholders

    def _get_fixed_fee_price(self):
        """
        Get the fixed fee price based on endpoint count.

        Returns:
            int: The fixed fee price
        """
        from data.pricing_tables import get_fixed_fee_price
        return get_fixed_fee_price(self.endpoint_count) or 0  # Default to 0 if None

    def get_sections_to_remove(self):
        """
        Get the sections to remove from the document.

        This method identifies sections in the document that should be removed based on
        the engagement options. For example, if EDR monitoring is set to "None", we remove
        the monitoring section from the document.

        The document template should have section markers in the format:
        <!-- BEGIN section_name -->
        ... content ...
        <!-- END section_name -->

        Returns:
            list: A list of section names to remove
        """
        sections_to_remove = []

        # Email platform-specific sections to remove
        if self.email_platform == "M365":
            sections_to_remove.extend([
                "exchange_analysis_section",
                "yahoo_analysis_section",
                "google_analysis_section",
                "other_email_analysis_section"
            ])
        elif self.email_platform == "Google":
            sections_to_remove.extend([
                "m365_analysis_section",
                "exchange_analysis_section",
                "yahoo_analysis_section",
                "other_email_analysis_section"
            ])
        elif self.email_platform == "Exchange":
            sections_to_remove.extend([
                "m365_analysis_section",
                "yahoo_analysis_section",
                "google_analysis_section",
                "other_email_analysis_section"
            ])
        elif self.email_platform == "Hybrid":
            sections_to_remove.extend([
                "yahoo_analysis_section",
                "google_analysis_section",
                "other_email_analysis_section"
            ])
        elif self.email_platform == "Other":
            sections_to_remove.extend([
                "m365_analysis_section",
                "exchange_analysis_section",
                "yahoo_analysis_section",
                "google_analysis_section"
            ])

        # EDR monitoring-specific sections to remove
        if self.edr_monitoring == "None":
            sections_to_remove.extend([
                "monitoring_section",
                "edr_monitoring_table_section",
                "edr_soc_fee_section"
            ])
        elif self.edr_monitoring == "Monitoring and Threat Hunting New Console":
            sections_to_remove.extend([
                "existing_console_section",
                "client_monitoring_section",
                "no_edr_section",
                "threat_hunting_only_section"
            ])
        elif self.edr_monitoring == "Monitoring and Threat Hunting Existing Console":
            sections_to_remove.extend([
                "new_console_section",
                "client_monitoring_section",
                "no_edr_section",
                "threat_hunting_only_section"
            ])
        elif self.edr_monitoring == "Threat Hunting Only":
            sections_to_remove.extend([
                "new_console_section",
                "existing_console_section",
                "client_monitoring_section",
                "no_edr_section"
            ])
        elif self.edr_monitoring == "Monitoring by Client":
            sections_to_remove.extend([
                "new_console_section",
                "existing_console_section",
                "no_edr_section",
                "threat_hunting_only_section"
            ])

        # Pricing model-specific sections to remove
        if self.is_fixed_fee:
            sections_to_remove.append("phase_breakdown_section")
        else:
            sections_to_remove.append("fixed_fee_section")

        # Always remove range pricing section - we only use single price now
        sections_to_remove.append("range_pricing_section")

        return sections_to_remove

    def calculate_pricing(self):
        """
        Calculate pricing for this DFIR engagement.

        Returns:
            dict: A dictionary containing pricing information
        """
        # Default pricing values
        DEFAULT_PRICING = {
            'dfir_rate': 450.0,  # Default rate
            'sp_total_hours': 0,
            'phase1_sp_hours': 0,
            'phase2_sp_hours': 0,
            'phase3_sp_hours': 0,
            'phase4_sp_hours': 0,
            'phase5_sp_hours': 0,
            'phase6_sp_hours': 0,
            'phase1_sp_costs': '$0',
            'phase2_sp_costs': '$0',
            'phase3_sp_costs': '$0',
            'phase4_sp_costs': '$0',
            'phase5_sp_costs': '$0',
            'phase6_sp_costs': '$0',
            'sp_costs': '$0',
            'sp_edr_fee': '$0',
            'sp_total_costs': '$0',
            'ffp_endpoint_count': 0,
            'ffp_total_cost': 0,
            'ffp_soc_fee': 0,
            'ffp_all_costs': 0
        }

        try:
            # Initialize pricing with default values
            pricing = DEFAULT_PRICING.copy()

            # Get the hourly rate for DFIR
            if self.client and self.client.insurance_carrier:
                pricing["dfir_rate"] = get_carrier_rate(self.client.insurance_carrier, "DFIR")

            # Determine which pricing model to use based on carrier and settings
            if self.is_fixed_fee or (self.client and is_fixed_fee_carrier(self.client.insurance_carrier)):
                return self._calculate_fixed_fee_pricing(pricing)
            elif self.client and self.client.insurance_carrier == "Beazley":
                return self._calculate_beazley_fixed_fee_pricing(pricing)
            elif self.client and self.client.law_firm == "The Beckage Firm":
                if self.is_fixed_fee:
                    return self._calculate_beckage_fixed_fee_pricing(pricing)
                else:
                    return self._calculate_beckage_hourly_pricing(pricing)
            elif self.client and self.client.insurance_carrier == "Chubb":
                return self._calculate_chubb_pricing(pricing)
            else:
                # Default to single price pricing
                return self._calculate_single_price_pricing(pricing)

        except Exception as e:
            print(f"Error in calculate_pricing: {e}")
            import traceback
            traceback.print_exc()
            # Return default pricing if an error occurs
            return DEFAULT_PRICING.copy()

    def _calculate_beckage_fixed_fee_pricing(self, pricing):
        """
        Calculate Beckage fixed fee pricing for this DFIR engagement.
        Uses the standard fixed fee pricing model but with Beckage template.

        Args:
            pricing (dict): The pricing dictionary to update

        Returns:
            dict: The updated pricing dictionary
        """
        # Use the standard fixed fee pricing calculation
        return self._calculate_fixed_fee_pricing(pricing)

    def _calculate_beckage_hourly_pricing(self, pricing):
        """
        Calculate Beckage hourly pricing for this DFIR engagement.
        Uses the standard single price pricing model but with Beckage template.

        Args:
            pricing (dict): The pricing dictionary to update

        Returns:
            dict: The updated pricing dictionary
        """
        # Use the standard single price pricing calculation
        return self._calculate_single_price_pricing(pricing)

    def _calculate_fixed_fee_pricing(self, pricing):
        """
        Calculate fixed fee pricing for this DFIR engagement.

        Args:
            pricing (dict): The pricing dictionary to update

        Returns:
            dict: The updated pricing dictionary
        """
        # Get the fixed fee price based on endpoint count
        pricing["ffp_endpoint_count"] = f"{self.endpoint_count:,}"
        pricing["ffp_total_cost"] = get_fixed_fee_price(self.endpoint_count)

        # Calculate EDR SOC monitoring fee if applicable
        if self.edr_monitoring in ["Monitoring and Threat Hunting", "Monitoring and Threat Hunting New Console"]:
            pricing["ffp_soc_fee"] = get_edr_soc_fee(self.endpoint_count)
            pricing["ffp_all_costs"] = pricing["ffp_total_cost"] + pricing["ffp_soc_fee"]
        else:
            pricing["ffp_soc_fee"] = 0
            pricing["ffp_all_costs"] = pricing["ffp_total_cost"]

        # Add default values for single price placeholders to avoid NoneType errors
        pricing["sp_total_hours"] = 0
        pricing["phase1_sp_hours"] = 0
        pricing["phase2_sp_hours"] = 0
        pricing["phase3_sp_hours"] = 0
        pricing["phase4_sp_hours"] = 0
        pricing["phase5_sp_hours"] = 0
        pricing["phase6_sp_hours"] = 0

        pricing["phase1_sp_costs"] = "$0"
        pricing["phase2_sp_costs"] = "$0"
        pricing["phase3_sp_costs"] = "$0"
        pricing["phase4_sp_costs"] = "$0"
        pricing["phase5_sp_costs"] = "$0"
        pricing["phase6_sp_costs"] = "$0"
        pricing["sp_costs"] = "$0"
        pricing["sp_edr_fee"] = f"${pricing['ffp_soc_fee']:,}"
        pricing["sp_total_costs"] = f"${pricing['ffp_all_costs']:,}"

        return pricing

    def _calculate_single_price_pricing(self, pricing):
        """
        Calculate single price pricing for this DFIR engagement.

        Args:
            pricing (dict): The pricing dictionary to update

        Returns:
            dict: The updated pricing dictionary
        """
        # Calculate hours for each phase
        pricing["sp_endpoint_count"] = f"{self.endpoint_count:,}"
        pricing["endpoint_count"] = f"{self.endpoint_count:,}"  # Add this for templates that use endpoint_count

        # Get the hourly rate (remove the $ sign if it's a string)
        hourly_rate = pricing["dfir_rate"]
        if isinstance(hourly_rate, str) and hourly_rate.startswith('$'):
            hourly_rate = int(hourly_rate.replace('$', '').replace(',', ''))

        # Phase 2: Triage & Forensic Analysis
        pricing["phase2_sp_hours"] = get_phase_hours(2, self.endpoint_count)
        pricing["phase2_hours"] = pricing["phase2_sp_hours"]  # Add this for templates that use phase2_hours
        pricing["phase2_sp_costs"] = int(hourly_rate * pricing["phase2_sp_hours"])
        pricing["phase2_costs"] = pricing["phase2_sp_costs"]  # Add this for templates that use phase2_costs

        # Phase 3: Log Analysis
        pricing["phase3_sp_hours"] = get_phase_hours(3, self.endpoint_count)
        pricing["phase3_hours"] = pricing["phase3_sp_hours"]  # Add this for templates that use phase3_hours
        pricing["phase3_sp_costs"] = int(hourly_rate * pricing["phase3_sp_hours"])
        pricing["phase3_costs"] = pricing["phase3_sp_costs"]  # Add this for templates that use phase3_costs

        # Phase 4: Email Tenant Analysis
        if self.email_platform == "Exchange":
            pricing["phase4_sp_hours"] = get_phase_hours(4, self.endpoint_count) + 10
        elif self.email_platform == "Hybrid":
            pricing["phase4_sp_hours"] = get_phase_hours(4, self.endpoint_count) + 15
        else:
            pricing["phase4_sp_hours"] = get_phase_hours(4, self.endpoint_count)
        pricing["phase4_hours"] = pricing["phase4_sp_hours"]  # Add this for templates that use phase4_hours
        pricing["phase4_sp_costs"] = int(hourly_rate * pricing["phase4_sp_hours"])
        pricing["phase4_costs"] = pricing["phase4_sp_costs"]  # Add this for templates that use phase4_costs

        # Phase 5: Endpoint Monitoring & Threat Hunting
        if self.edr_monitoring != "None":
            pricing["phase5_sp_hours"] = get_phase_hours(5, self.endpoint_count)
            pricing["phase5_hours"] = pricing["phase5_sp_hours"]  # Add this for templates that use phase5_hours
            pricing["phase5_sp_costs"] = int(hourly_rate * pricing["phase5_sp_hours"])
            pricing["phase5_costs"] = pricing["phase5_sp_costs"]  # Add this for templates that use phase5_costs

            # Only apply EDR SOC fee if monitoring is selected
            print(f"EDR Monitoring option: {self.edr_monitoring}")
            # Check if the EDR monitoring option contains both 'Monitoring' and 'Threat Hunting'
            if "Monitoring" in self.edr_monitoring and "Threat Hunting" in self.edr_monitoring:
                pricing["sp_edr_fee"] = get_edr_soc_fee(self.endpoint_count)
                pricing["edr_fee"] = pricing["sp_edr_fee"]  # Add this for templates that use edr_fee
                print(f"EDR SOC fee applied: {pricing['sp_edr_fee']}")
            else:
                pricing["sp_edr_fee"] = 0
                pricing["edr_fee"] = 0  # Add this for templates that use edr_fee
                print("EDR SOC fee not applied (0)")
        else:
            pricing["phase5_sp_hours"] = 0
            pricing["phase5_hours"] = 0  # Add this for templates that use phase5_hours
            pricing["phase5_sp_costs"] = 0
            pricing["phase5_costs"] = 0  # Add this for templates that use phase5_costs
            pricing["sp_edr_fee"] = 0
            pricing["edr_fee"] = 0  # Add this for templates that use edr_fee

        # Phase 6: Report Writing
        pricing["phase6_sp_hours"] = 15
        pricing["phase6_hours"] = pricing["phase6_sp_hours"]  # Add this for templates that use phase6_hours
        pricing["phase6_sp_costs"] = int(hourly_rate * pricing["phase6_sp_hours"])
        pricing["phase6_costs"] = pricing["phase6_sp_costs"]  # Add this for templates that use phase6_costs

        # Phase 1: Project Management & Planning - use table lookup
        pricing["phase1_sp_hours"] = get_phase_hours(1, self.endpoint_count)
        pricing["phase1_hours"] = pricing["phase1_sp_hours"]
        pricing["phase1_sp_costs"] = int(hourly_rate * pricing["phase1_sp_hours"])
        pricing["phase1_costs"] = pricing["phase1_sp_costs"]

        # Calculate totals
        pricing["sp_total_hours"] = (pricing["phase1_sp_hours"] + pricing["phase2_sp_hours"] +
                                    pricing["phase3_sp_hours"] + pricing["phase4_sp_hours"] +
                                    pricing["phase5_sp_hours"] + pricing["phase6_sp_hours"])
        pricing["total_hours"] = pricing["sp_total_hours"]  # Add this for templates that use total_hours

        # Calculate the total labor costs (sum of all phase costs)
        total_labor_costs = (pricing["phase1_sp_costs"] + pricing["phase2_sp_costs"] +
                           pricing["phase3_sp_costs"] + pricing["phase4_sp_costs"] +
                           pricing["phase5_sp_costs"] + pricing["phase6_sp_costs"])
        pricing["sp_costs"] = total_labor_costs
        pricing["labor_costs"] = total_labor_costs  # Add this for templates that use labor_costs

        # Format the individual phase costs as currency strings
        pricing["phase1_sp_costs"] = f"${pricing['phase1_sp_costs']:,}"
        pricing["phase1_costs"] = pricing["phase1_sp_costs"]  # Add this for templates that use phase1_costs
        pricing["phase2_sp_costs"] = f"${pricing['phase2_sp_costs']:,}"
        pricing["phase2_costs"] = pricing["phase2_sp_costs"]  # Add this for templates that use phase2_costs
        pricing["phase3_sp_costs"] = f"${pricing['phase3_sp_costs']:,}"
        pricing["phase3_costs"] = pricing["phase3_sp_costs"]  # Add this for templates that use phase3_costs
        pricing["phase4_sp_costs"] = f"${pricing['phase4_sp_costs']:,}"
        pricing["phase4_costs"] = pricing["phase4_sp_costs"]  # Add this for templates that use phase4_costs
        pricing["phase5_sp_costs"] = f"${pricing['phase5_sp_costs']:,}"
        pricing["phase5_costs"] = pricing["phase5_sp_costs"]  # Add this for templates that use phase5_costs
        pricing["phase6_sp_costs"] = f"${pricing['phase6_sp_costs']:,}"
        pricing["phase6_costs"] = pricing["phase6_sp_costs"]  # Add this for templates that use phase6_costs

        # Store the raw values for calculations
        raw_labor_costs = pricing["sp_costs"]
        raw_edr_fee = pricing["sp_edr_fee"]

        # Format the labor costs
        pricing["sp_costs"] = f"${raw_labor_costs:,}"
        pricing["labor_costs"] = pricing["sp_costs"]  # Add this for templates that use labor_costs
        pricing["Estimated Labor Costs"] = pricing["sp_costs"]  # Add this for templates that use Estimated Labor Costs

        # Format the EDR fee
        pricing["sp_edr_fee"] = f"${raw_edr_fee:,}"
        pricing["edr_fee"] = pricing["sp_edr_fee"]  # Add this for templates that use edr_fee
        pricing["EDR SOC Monitoring Fee"] = pricing["sp_edr_fee"]  # Add this for templates that use EDR SOC Monitoring Fee

        # Calculate and format the total costs
        total_costs = raw_labor_costs + raw_edr_fee
        pricing["sp_total_costs"] = f"${total_costs:,}"
        pricing["total_costs"] = pricing["sp_total_costs"]  # Add this for templates that use total_costs
        pricing["Total Estimated Costs"] = pricing["sp_total_costs"]  # Add this for templates that use Total Estimated Costs

        return pricing


    # All pricing now uses the single price model

    def _calculate_beazley_fixed_fee_pricing(self, pricing):
        """
        Calculate Beazley fixed fee pricing for this DFIR engagement.

        Args:
            pricing (dict): The pricing dictionary to update

        Returns:
            dict: The updated pricing dictionary
        """
        # Get the fixed fee price based on endpoint count and whether there are onsite collections
        pricing["beazley_ffp_endpoint_count"] = f"{self.endpoint_count:,}"

        # Get Beazley fixed fee price with error handling
        try:
            # Check if rr_is_remote attribute exists, default to False if not
            rr_is_remote = getattr(self, 'rr_is_remote', False)
            beazley_ffp_est_cost = get_beazley_fixed_fee_price(self.endpoint_count, not rr_is_remote)
            if beazley_ffp_est_cost is None:
                beazley_ffp_est_cost = 0
            pricing["beazley_ffp_est_cost"] = beazley_ffp_est_cost
        except Exception as e:
            print(f"Error getting Beazley fixed fee price: {e}")
            pricing["beazley_ffp_est_cost"] = 0

        # Calculate EDR SOC monitoring fee if applicable
        try:
            if self.edr_monitoring in ["Monitoring and Threat Hunting", "Monitoring and Threat Hunting New Console"]:
                edr_fee = get_edr_soc_fee(self.endpoint_count)
                if edr_fee is None:
                    edr_fee = 0
                pricing["beazley_edr_fee"] = edr_fee
            else:
                pricing["beazley_edr_fee"] = 0
        except Exception as e:
            print(f"Error getting EDR SOC fee: {e}")
            pricing["beazley_edr_fee"] = 0

        # Calculate total costs
        pricing["beazley_total_costs"] = pricing["beazley_ffp_est_cost"] + pricing["beazley_edr_fee"]

        return pricing


    # All Beazley pricing now uses the fixed fee model

    def _calculate_chubb_pricing(self, pricing):
        """
        Calculate Chubb pricing for this DFIR engagement.

        Args:
            pricing (dict): The pricing dictionary to update

        Returns:
            dict: The updated pricing dictionary
        """
        # Get the hourly rate for Chubb if not already set
        if "dfir_rate" not in pricing:
            pricing["dfir_rate"] = get_carrier_rate("Chubb", "DFIR")

        # Ensure dfir_rate is a float
        if isinstance(pricing["dfir_rate"], str):
            pricing["dfir_rate"] = float(pricing["dfir_rate"].replace('$', '').replace(',', ''))

        # Calculate hours for each phase
        pricing["chubb_endpoint_count"] = f"{self.endpoint_count:,}"

        # Add the mandatory $599 fee for Chubb
        pricing["chubb_mandatory_fee"] = 599

        # CS120 – Digital Forensic Analysis (Phase 2)
        pricing["chubb_phase2_hours"] = get_phase_hours(2, self.endpoint_count)
        pricing["chubb_phase2_costs"] = int(float(pricing["dfir_rate"]) * pricing["chubb_phase2_hours"])

        # Add CS code placeholders for Phase 2
        pricing["cs120_forensic_hours"] = pricing["chubb_phase2_hours"]
        pricing["cs120_forensic_costs"] = pricing["chubb_phase2_costs"]

        # CS120 – Email Tenant Analysis (Phase 3) and Log Analysis (Phase 4)
        # For Chubb, we combine Phase 3 and Phase 4 into CS120 Email
        phase3_base_hours = get_phase_hours(3, self.endpoint_count)
        phase4_base_hours = get_phase_hours(4, self.endpoint_count)

        if self.email_platform == "Exchange":
            phase3_hours = phase3_base_hours
            phase4_hours = phase4_base_hours + 10
        elif self.email_platform == "Hybrid":
            phase3_hours = phase3_base_hours
            phase4_hours = phase4_base_hours + 15
        else:
            phase3_hours = phase3_base_hours
            phase4_hours = phase4_base_hours

        # Combine Phase 3 and Phase 4 hours for Chubb CS120 Email
        pricing["chubb_phase3_hours"] = phase3_hours + phase4_hours
        pricing["chubb_phase3_costs"] = int(float(pricing["dfir_rate"]) * pricing["chubb_phase3_hours"])

        # Add CS code placeholders for combined Phase 3 and 4
        pricing["cs120_email_hours"] = pricing["chubb_phase3_hours"]
        pricing["cs120_email_costs"] = pricing["chubb_phase3_costs"]

        # CS210 – Endpoint Monitoring & Threat Hunting Analysis (Phase 5)
        if self.edr_monitoring != "None":
            pricing["chubb_phase4_hours"] = get_phase_hours(5, self.endpoint_count)
            pricing["chubb_phase4_costs"] = int(float(pricing["dfir_rate"]) * pricing["chubb_phase4_hours"])

            # Add CS code placeholders for Phase 5
            pricing["cs210_hours"] = pricing["chubb_phase4_hours"]
            pricing["cs210_costs"] = pricing["chubb_phase4_costs"]

            if self.edr_monitoring in ["Monitoring and Threat Hunting", "Monitoring and Threat Hunting New Console"]:
                pricing["chubb_edr_fee"] = get_edr_soc_fee(self.endpoint_count)
            else:
                pricing["chubb_edr_fee"] = 0
        else:
            pricing["chubb_phase4_hours"] = 0
            pricing["chubb_phase4_costs"] = 0
            pricing["chubb_edr_fee"] = 0
            pricing["cs210_hours"] = 0
            pricing["cs210_costs"] = 0

        # CS430 – Final Report (Phase 6)
        pricing["chubb_report_hours"] = 15
        pricing["chubb_report_cost"] = int(float(pricing["dfir_rate"]) * pricing["chubb_report_hours"])

        # Add CS code placeholders for Phase 6
        pricing["cs430_hours"] = pricing["chubb_report_hours"]
        pricing["cs430_costs"] = pricing["chubb_report_cost"]

        # Calculate subtotal for phases 2-6
        subtotal_hours = (pricing["chubb_phase2_hours"] + pricing["chubb_phase3_hours"] +
                         pricing["chubb_phase4_hours"] + pricing["chubb_report_hours"])

        # CS410 - Project Management, Planning & Reporting (Phase 1)
        # Use endpoint-based hours instead of percentage calculation
        pricing["chubb_phase1_hours"] = get_phase_hours(1, self.endpoint_count)
        pricing["chubb_phase1_cost"] = int(float(pricing["dfir_rate"]) * pricing["chubb_phase1_hours"])

        # Add CS code placeholders for Phase 1
        pricing["cs410_hours"] = pricing["chubb_phase1_hours"]
        pricing["cs410_costs"] = pricing["chubb_phase1_cost"]

        # Calculate totals
        pricing["chubb_total_hours"] = (pricing["chubb_phase1_hours"] + pricing["chubb_phase2_hours"] +
                                      pricing["chubb_phase3_hours"] + pricing["chubb_phase4_hours"] +
                                      pricing["chubb_report_hours"])
        pricing["chubb_costs"] = (pricing["chubb_phase1_cost"] + pricing["chubb_phase2_costs"] +
                                pricing["chubb_phase3_costs"] + pricing["chubb_phase4_costs"] +
                                pricing["chubb_report_cost"])

        # Add the mandatory $599 fee to the total costs
        pricing["chubb_total_costs"] = pricing["chubb_costs"] + pricing["chubb_edr_fee"] + pricing["chubb_mandatory_fee"]

        # Add total placeholders for CS code template
        pricing["total_hours"] = pricing["chubb_total_hours"]
        pricing["total_labor_costs"] = pricing["chubb_costs"]
        pricing["edr_fee"] = pricing["chubb_edr_fee"]
        pricing["mandatory_fee"] = pricing["chubb_mandatory_fee"]
        pricing["total_costs"] = pricing["chubb_total_costs"]

        return pricing
