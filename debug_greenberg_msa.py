#!/usr/bin/env python3
"""
Debug script for Greenberg Traurig MSA generation issues.
"""

import os
import sys
import tempfile
from datetime import datetime

def test_greenberg_msa_generation():
    """Test MSA generation for Greenberg Traurig specifically"""
    print("=" * 60)
    print("DEBUGGING GREENBERG TRAURIG MSA GENERATION")
    print("=" * 60)
    
    try:
        # Import required modules
        from models.dfir import DFIREngagement
        from models.client import Client
        from utils.document_generator import DocumentGenerator
        
        # Create a test client with <PERSON> Traurig
        client = Client()
        client.name = "Test Client Corp"
        client.address = "123 Test Street, Test City, TS 12345"
        client.law_firm = "Greenberg Traurig, LLP"  # Note the comma
        client.insurance_carrier = "Test Insurance"
        
        print(f"✓ Client created: {client.name}")
        print(f"✓ Law firm: {client.law_firm}")
        
        # Create a test engagement
        engagement = DFIREngagement()
        engagement.client = client
        engagement.contact_name = "<PERSON>"
        engagement.contact_email = "<EMAIL>"
        engagement.contact_phone = "************"
        engagement.engagement_type = "IR Investigation"
        
        print(f"✓ Engagement created")
        
        # Test MSA template path resolution
        msa_template_path = engagement.get_msa_template_path()
        print(f"✓ MSA template path: {msa_template_path}")
        
        # Check if template exists
        from utils.path_resolver import resolve_template_path
        resolved_path = resolve_template_path(msa_template_path)
        print(f"✓ Resolved template path: {resolved_path}")
        print(f"✓ Template exists: {os.path.exists(resolved_path)}")
        
        # Test placeholder generation
        placeholders = engagement.get_placeholders()
        print(f"\n✓ Generated {len(placeholders)} placeholders")
        
        # Check for legal_date specifically
        if 'legal_date' in placeholders:
            print(f"✓ legal_date found: '{placeholders['legal_date']}'")
        else:
            print("✗ legal_date NOT found in placeholders")
        
        # Show all date-related placeholders
        print("\nDate-related placeholders:")
        for key, value in placeholders.items():
            if 'date' in key.lower():
                print(f"  {key}: {value}")
        
        # Test document generation
        print(f"\n" + "=" * 40)
        print("TESTING DOCUMENT GENERATION")
        print("=" * 40)
        
        with tempfile.TemporaryDirectory() as temp_dir:
            print(f"✓ Created temp directory: {temp_dir}")
            
            # Create document generator
            doc_gen = DocumentGenerator(engagement, temp_dir)
            
            # Get placeholders from document generator
            doc_placeholders = doc_gen._get_placeholders()
            print(f"✓ Document generator created {len(doc_placeholders)} placeholders")
            
            # Check legal_date in document generator placeholders
            if 'legal_date' in doc_placeholders:
                print(f"✓ legal_date in doc generator: '{doc_placeholders['legal_date']}'")
            else:
                print("✗ legal_date NOT found in document generator placeholders")
            
            # Show differences between engagement and doc generator placeholders
            print("\nPlaceholder comparison:")
            all_keys = set(placeholders.keys()) | set(doc_placeholders.keys())
            for key in sorted(all_keys):
                eng_val = placeholders.get(key, "MISSING")
                doc_val = doc_placeholders.get(key, "MISSING")
                if eng_val != doc_val:
                    print(f"  {key}: ENG='{eng_val}' vs DOC='{doc_val}'")
            
            # Try to generate MSA
            print(f"\n" + "=" * 40)
            print("GENERATING MSA DOCUMENT")
            print("=" * 40)
            
            try:
                msa_path = doc_gen.generate_msa()
                if msa_path and os.path.exists(msa_path):
                    file_size = os.path.getsize(msa_path)
                    print(f"✓ MSA generated successfully: {msa_path}")
                    print(f"✓ File size: {file_size} bytes")
                    
                    # Check if it's just a copy or actually processed
                    if file_size > 0:
                        print("✓ File appears to have content")
                    else:
                        print("✗ File is empty")
                        
                else:
                    print("✗ MSA generation failed or file not created")
                    
            except Exception as e:
                print(f"✗ Error generating MSA: {e}")
                import traceback
                traceback.print_exc()
        
        return True
        
    except Exception as e:
        print(f"✗ Error in test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_legal_date_generation():
    """Test the legal date generation specifically"""
    print(f"\n" + "=" * 40)
    print("TESTING LEGAL DATE GENERATION")
    print("=" * 40)
    
    try:
        from utils.document_generator import DocumentGenerator
        
        # Test the ordinal suffix function
        def get_ordinal_suffix(day):
            """Get the ordinal suffix for a day (st, nd, rd, th)"""
            if 10 <= day % 100 <= 20:
                suffix = 'th'
            else:
                suffix = {1: 'st', 2: 'nd', 3: 'rd'}.get(day % 10, 'th')
            return suffix
        
        # Test various dates
        test_dates = [1, 2, 3, 4, 11, 21, 22, 23, 31]
        for day in test_dates:
            suffix = get_ordinal_suffix(day)
            print(f"  Day {day}: {day}{suffix}")
        
        # Test current date
        today = datetime.now()
        day_ordinal = get_ordinal_suffix(today.day)
        legal_date = f"this {today.day}{day_ordinal} day of {today.strftime('%B')} {today.year}"
        print(f"\n✓ Current legal date: {legal_date}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error testing legal date: {e}")
        return False

def main():
    """Main test function"""
    print("PACE v1.1.4 - Greenberg Traurig MSA Debug")
    print("=" * 60)
    
    # Test legal date generation
    test_legal_date_generation()
    
    # Test Greenberg MSA generation
    test_greenberg_msa_generation()
    
    print(f"\n" + "=" * 60)
    print("DEBUG COMPLETE")
    print("=" * 60)
    
    print("\nIf issues found:")
    print("1. Check template file exists and is accessible")
    print("2. Verify law firm name matches exactly")
    print("3. Check placeholder generation in document generator")
    print("4. Verify docxtpl is working correctly")
    print("5. Check for font/formatting issues in template")

if __name__ == "__main__":
    main()
