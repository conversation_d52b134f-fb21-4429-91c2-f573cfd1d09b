#!/usr/bin/env python3
"""
Test script to verify icon display in the executable.
"""

import os
import subprocess
import time

def test_executable_icon():
    """Test if the executable displays the correct icon"""
    print("Testing executable icon display...")
    
    exe_path = "dist/PACE.exe"
    
    if not os.path.exists(exe_path):
        print("✗ PACE.exe not found")
        return False
    
    print("✓ PACE.exe found")
    
    try:
        # Start the executable
        print("Starting PACE.exe to test icon...")
        process = subprocess.Popen([exe_path], 
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE)
        
        # Wait a moment for it to start
        time.sleep(3)
        
        # Check if it's running
        if process.poll() is None:
            print("✓ PACE.exe started successfully")
            print("✓ Check the taskbar and window title for the PACE icon")
            print("✓ The icon should appear as a blue square with 'PACE' text")
            
            # Terminate the process
            process.terminate()
            process.wait(timeout=5)
            print("✓ Process terminated cleanly")
            return True
        else:
            print("✗ PACE.exe failed to start or exited immediately")
            stdout, stderr = process.communicate()
            if stderr:
                print(f"Error: {stderr.decode()[:200]}")
            return False
            
    except subprocess.TimeoutExpired:
        print("✗ Process didn't terminate cleanly")
        try:
            process.kill()
        except:
            pass
        return False
    except Exception as e:
        print(f"✗ Error testing executable: {e}")
        return False

def check_icon_file():
    """Check the icon file properties"""
    print("Checking PACE.ico properties...")
    
    if not os.path.exists("PACE.ico"):
        print("✗ PACE.ico not found")
        return False
    
    # Get file size
    size = os.path.getsize("PACE.ico")
    print(f"✓ Icon file size: {size} bytes")
    
    if size < 1000:
        print("⚠ Icon file seems very small, might be corrupted")
        return False
    elif size > 100000:
        print("⚠ Icon file seems very large, might be inefficient")
    else:
        print("✓ Icon file size looks reasonable")
    
    return True

def main():
    """Main test function"""
    print("PACE Icon Display Test")
    print("=" * 30)
    
    # Check icon file
    icon_ok = check_icon_file()
    
    print()
    
    # Test executable
    exe_ok = test_executable_icon()
    
    print("\n" + "=" * 30)
    if icon_ok and exe_ok:
        print("✓ ICON TEST COMPLETED")
        print("\nTo verify the icon is working:")
        print("1. Run dist/PACE.exe")
        print("2. Look at the taskbar - you should see a blue PACE icon")
        print("3. Look at the window title bar - should show PACE icon")
        print("4. If you see the default Python icon, the embedding failed")
    else:
        print("✗ ICON TEST HAD ISSUES")
        print("\nTroubleshooting:")
        print("1. Ensure PACE.ico exists and is valid")
        print("2. Check PyInstaller spec file icon setting")
        print("3. Try rebuilding with --clean flag")
    
    return icon_ok and exe_ok

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
