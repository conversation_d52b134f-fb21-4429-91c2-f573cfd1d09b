@echo off
setlocal enabledelayedexpansion

echo =====================================
echo PACE v1.1.4 - Auto Installer Builder
echo =====================================
echo This script will automatically:
echo 1. Build PACE executable
echo 2. Download Inno Setup if needed
echo 3. Create professional installer
echo 4. Package for distribution
echo.

REM Check if we're in the right directory
if not exist "main.py" (
    echo Error: main.py not found!
    echo Please run this script from the PACE root directory.
    pause
    exit /b 1
)

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python not found. Please install Python 3.9+ first.
    echo Download from: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✓ Python found
echo.

echo [1/5] Building PACE executable...
echo =====================================

python build_executable.py

if not exist "dist\PACE.exe" (
    echo ✗ Build FAILED!
    pause
    exit /b 1
)

for %%A in ("dist\PACE.exe") do set "EXE_SIZE=%%~zA"
set /a "EXE_SIZE_MB=!EXE_SIZE! / 1048576"
echo ✓ PACE.exe created (!EXE_SIZE_MB! MB)
echo.

echo [2/5] Checking for Inno Setup...
echo =====================================

set "INNO_FOUND=0"
set "ISCC_PATH="

if exist "C:\Program Files (x86)\Inno Setup 6\iscc.exe" (
    set "ISCC_PATH=C:\Program Files (x86)\Inno Setup 6\iscc.exe"
    set "INNO_FOUND=1"
) else if exist "C:\Program Files\Inno Setup 6\iscc.exe" (
    set "ISCC_PATH=C:\Program Files\Inno Setup 6\iscc.exe"
    set "INNO_FOUND=1"
) else (
    where iscc.exe >nul 2>&1
    if !errorlevel! equ 0 (
        for /f "tokens=*" %%i in ('where iscc.exe') do set "ISCC_PATH=%%i"
        set "INNO_FOUND=1"
    )
)

if !INNO_FOUND! equ 1 (
    echo ✓ Inno Setup found
) else (
    echo ⚠ Inno Setup not found - downloading...
    echo.
    echo [3/5] Installing Inno Setup...
    echo =====================================
    
    REM Create temp directory
    if not exist "temp" mkdir temp
    
    echo Downloading Inno Setup installer...
    powershell -Command "& {Invoke-WebRequest -Uri 'https://jrsoftware.org/download.php/is.exe' -OutFile 'temp\innosetup.exe'}"
    
    if exist "temp\innosetup.exe" (
        echo ✓ Downloaded successfully
        echo Installing Inno Setup (this may take a moment)...
        
        REM Install silently
        start /wait "temp\innosetup.exe" /SILENT /SUPPRESSMSGBOXES
        
        REM Check if installation was successful
        if exist "C:\Program Files (x86)\Inno Setup 6\iscc.exe" (
            set "ISCC_PATH=C:\Program Files (x86)\Inno Setup 6\iscc.exe"
            set "INNO_FOUND=1"
            echo ✓ Inno Setup installed successfully
        ) else if exist "C:\Program Files\Inno Setup 6\iscc.exe" (
            set "ISCC_PATH=C:\Program Files\Inno Setup 6\iscc.exe"
            set "INNO_FOUND=1"
            echo ✓ Inno Setup installed successfully
        ) else (
            echo ✗ Installation may have failed
            echo Please install manually from: https://jrsoftware.org/isinfo.php
            set "INNO_FOUND=0"
        )
        
        REM Clean up
        del "temp\innosetup.exe" >nul 2>&1
        rmdir "temp" >nul 2>&1
    ) else (
        echo ✗ Download failed
        echo Please install manually from: https://jrsoftware.org/isinfo.php
        set "INNO_FOUND=0"
    )
)

echo.
echo [4/5] Creating installer...
echo =====================================

if !INNO_FOUND! equ 1 (
    if not exist "installer" mkdir installer
    
    echo Compiling installer...
    "!ISCC_PATH!" PACE_installer.iss
    
    if exist "installer\PACE_v1.1.4_Setup.exe" (
        for %%A in ("installer\PACE_v1.1.4_Setup.exe") do set "INSTALLER_SIZE=%%~zA"
        set /a "INSTALLER_SIZE_MB=!INSTALLER_SIZE! / 1048576"
        echo ✓ Installer created (!INSTALLER_SIZE_MB! MB)
        set "INSTALLER_CREATED=1"
    ) else (
        echo ✗ Installer creation failed
        set "INSTALLER_CREATED=0"
    )
) else (
    echo ⚠ Skipping installer creation (Inno Setup not available)
    set "INSTALLER_CREATED=0"
)

echo.
echo [5/5] Creating distribution package...
echo =====================================

if not exist "distribution" mkdir distribution

copy "dist\PACE.exe" "distribution\" >nul
copy "README.md" "distribution\" >nul 2>&1
copy "CHANGELOG.md" "distribution\" >nul 2>&1
copy "LICENSE.txt" "distribution\" >nul 2>&1

if !INSTALLER_CREATED! equ 1 (
    copy "installer\PACE_v1.1.4_Setup.exe" "distribution\" >nul
)

REM Create quick start guide
echo PACE v1.1.4 - Quick Start Guide > "distribution\QUICK_START.txt"
echo ================================= >> "distribution\QUICK_START.txt"
echo. >> "distribution\QUICK_START.txt"
echo OPTION 1: Standalone (Immediate Use) >> "distribution\QUICK_START.txt"
echo   1. Double-click PACE.exe >> "distribution\QUICK_START.txt"
echo   2. Start using PACE immediately >> "distribution\QUICK_START.txt"
echo   3. No installation required >> "distribution\QUICK_START.txt"
echo. >> "distribution\QUICK_START.txt"

if !INSTALLER_CREATED! equ 1 (
    echo OPTION 2: Professional Installation >> "distribution\QUICK_START.txt"
    echo   1. Double-click PACE_v1.1.4_Setup.exe >> "distribution\QUICK_START.txt"
    echo   2. Follow the installation wizard >> "distribution\QUICK_START.txt"
    echo   3. Launch from Start Menu or Desktop >> "distribution\QUICK_START.txt"
    echo. >> "distribution\QUICK_START.txt"
)

echo System Requirements: >> "distribution\QUICK_START.txt"
echo - Windows 10 or later >> "distribution\QUICK_START.txt"
echo - No Python or other software needed >> "distribution\QUICK_START.txt"

echo ✓ Distribution package ready
echo.

echo =====================================
echo SUCCESS! ALL FILES READY
echo =====================================
echo.
echo Your team can now use:
echo.
echo 📁 distribution\PACE.exe
echo    ↳ Standalone executable (!EXE_SIZE_MB! MB)
echo    ↳ No installation needed
echo    ↳ Just run and go!
echo.

if !INSTALLER_CREATED! equ 1 (
    echo 📦 distribution\PACE_v1.1.4_Setup.exe
    echo    ↳ Professional installer (!INSTALLER_SIZE_MB! MB)
    echo    ↳ Standard Windows installation
    echo    ↳ Creates shortcuts and uninstaller
    echo.
)

echo 📋 distribution\QUICK_START.txt
echo    ↳ Instructions for your team
echo.

echo DISTRIBUTION METHODS:
echo =====================================
echo 1. Email: Send PACE.exe (single file)
echo 2. Network: Copy to shared folder
echo 3. USB: Copy distribution folder
echo 4. Cloud: Upload to Google Drive/OneDrive
echo.

echo Opening distribution folder...
explorer "distribution"

echo.
echo Press any key to exit...
pause >nul
