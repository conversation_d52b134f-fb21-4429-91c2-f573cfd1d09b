#!/usr/bin/env python3
"""
Test the improved Recovery and Remediation (R&R) functionality.
Tests the new conditional R&R SOW generation based on user selection.
"""

import tempfile
import os

def test_rr_conditional_generation():
    """Test that R&R SOW is only generated when explicitly requested"""
    print("RECOVERY & REMEDIATION CONDITIONAL GENERATION TEST")
    print("=" * 60)
    
    try:
        from models.dfir import DFIREngagement
        from models.client import Client
        from utils.document_generator import DocumentGenerator
        
        # Create a test client
        client = Client()
        client.name = "Test Corporation"
        client.law_firm = "Test Law Firm"
        client.insurance_carrier = "Standard"
        
        print("✓ Created test client")
        
        # Test Case 1: R&R needed but SOW generation not requested
        print("\n1. R&R Needed but SOW Generation NOT Requested:")
        print("-" * 50)
        
        dfir1 = DFIREngagement(client)
        dfir1.endpoint_count = 500
        dfir1.include_rr = True          # R&R is needed
        dfir1.generate_rr_sow = False    # But don't generate SOW document
        dfir1.rr_is_remote = True
        
        with tempfile.TemporaryDirectory() as temp_dir:
            doc_gen1 = DocumentGenerator(dfir1, temp_dir)
            documents1 = doc_gen1.generate_documents()
            
            # Check if R&R SOW was generated
            rr_sow_generated = any("Recovery" in doc or "RR" in doc for doc in documents1)
            
            print(f"  Include R&R: {dfir1.include_rr}")
            print(f"  Generate R&R SOW: {dfir1.generate_rr_sow}")
            print(f"  Documents generated: {len(documents1)}")
            print(f"  R&R SOW generated: {rr_sow_generated}")
            
            if not rr_sow_generated:
                print("  ✓ PASS: R&R SOW correctly NOT generated")
            else:
                print("  ✗ FAIL: R&R SOW was generated when it shouldn't be")
        
        # Test Case 2: R&R needed and SOW generation requested
        print("\n2. R&R Needed and SOW Generation Requested:")
        print("-" * 50)
        
        dfir2 = DFIREngagement(client)
        dfir2.endpoint_count = 500
        dfir2.include_rr = True          # R&R is needed
        dfir2.generate_rr_sow = True     # Generate SOW document
        dfir2.rr_is_remote = False
        dfir2.onsite_resources_count = 2
        
        with tempfile.TemporaryDirectory() as temp_dir:
            doc_gen2 = DocumentGenerator(dfir2, temp_dir)
            documents2 = doc_gen2.generate_documents()
            
            # Check if R&R SOW was generated
            rr_sow_generated = any("Recovery" in doc or "RR" in doc for doc in documents2)
            
            print(f"  Include R&R: {dfir2.include_rr}")
            print(f"  Generate R&R SOW: {dfir2.generate_rr_sow}")
            print(f"  Documents generated: {len(documents2)}")
            print(f"  R&R SOW generated: {rr_sow_generated}")
            
            if rr_sow_generated:
                print("  ✓ PASS: R&R SOW correctly generated")
            else:
                print("  ✗ FAIL: R&R SOW was not generated when it should be")
        
        # Test Case 3: R&R not needed
        print("\n3. R&R Not Needed:")
        print("-" * 50)
        
        dfir3 = DFIREngagement(client)
        dfir3.endpoint_count = 500
        dfir3.include_rr = False         # R&R is not needed
        dfir3.generate_rr_sow = False    # Don't generate SOW document
        
        with tempfile.TemporaryDirectory() as temp_dir:
            doc_gen3 = DocumentGenerator(dfir3, temp_dir)
            documents3 = doc_gen3.generate_documents()
            
            # Check if R&R SOW was generated
            rr_sow_generated = any("Recovery" in doc or "RR" in doc for doc in documents3)
            
            print(f"  Include R&R: {dfir3.include_rr}")
            print(f"  Generate R&R SOW: {dfir3.generate_rr_sow}")
            print(f"  Documents generated: {len(documents3)}")
            print(f"  R&R SOW generated: {rr_sow_generated}")
            
            if not rr_sow_generated:
                print("  ✓ PASS: R&R SOW correctly NOT generated")
            else:
                print("  ✗ FAIL: R&R SOW was generated when R&R not needed")
        
        # Test Case 4: Test R&R placeholders are still generated for pricing
        print("\n4. R&R Placeholders for Pricing (R&R needed but no SOW):")
        print("-" * 60)
        
        dfir4 = DFIREngagement(client)
        dfir4.endpoint_count = 500
        dfir4.include_rr = True          # R&R is needed for pricing
        dfir4.generate_rr_sow = False    # But don't generate SOW document
        dfir4.rr_is_remote = False
        dfir4.onsite_resources_count = 3
        
        placeholders = dfir4.get_placeholders()
        
        # Check if R&R placeholders exist
        rr_placeholders = {k: v for k, v in placeholders.items() if 'rr_' in k.lower()}
        
        print(f"  Include R&R: {dfir4.include_rr}")
        print(f"  Generate R&R SOW: {dfir4.generate_rr_sow}")
        print(f"  R&R placeholders found: {len(rr_placeholders)}")
        
        for key, value in rr_placeholders.items():
            print(f"    {key}: {value}")
        
        if rr_placeholders:
            print("  ✓ PASS: R&R placeholders generated for pricing")
        else:
            print("  ✗ FAIL: R&R placeholders not generated")
        
        print("\n" + "=" * 60)
        print("R&R CONDITIONAL GENERATION TEST COMPLETE")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"✗ Error during test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_rr_conditional_generation()
    exit(0 if success else 1)
