#!/usr/bin/env python3
"""
Test Greenberg Traurig MSA to verify legal_date placeholder and font consistency
"""

import os
import tempfile
from datetime import datetime

def get_ordinal_suffix(day):
    """Get the ordinal suffix for a day (1st, 2nd, 3rd, etc.)"""
    if 10 <= day % 100 <= 20:
        suffix = 'th'
    else:
        suffix = {1: 'st', 2: 'nd', 3: 'rd'}.get(day % 10, 'th')
    return suffix

def test_greenberg_msa():
    """Test Greenberg Traurig MSA generation"""
    print("GREENBERG TRAURIG MSA TEST")
    print("=" * 50)
    
    try:
        # Import required modules
        from models.dfir import DFIREngagement
        from models.client import Client
        from utils.document_generator import DocumentGenerator
        
        # Create a test client with Greenberg Traurig
        client = Client()
        client.name = "Test Corporation & Associates"  # Include ampersand to test font handling
        client.address = "123 Main Street, Suite 100\nAnytown, ST 12345"
        client.law_firm = "<PERSON> Traurig, LLP"
        client.contact_name = "<PERSON>"
        client.contact_email = "<EMAIL>"
        client.contact_phone = "************"
        
        print(f"✓ Created test client: {client.name}")
        print(f"✓ Law firm: {client.law_firm}")
        
        # Create DFIR engagement
        dfir = DFIREngagement(client)
        dfir.endpoint_count = 500
        dfir.engagement_type = "IR Investigation"
        
        print(f"✓ Created DFIR engagement with {dfir.endpoint_count} endpoints")
        
        # Test MSA template path
        msa_template_path = dfir.get_msa_template_path()
        print(f"✓ MSA template path: {msa_template_path}")
        
        # Verify it's the Greenberg template
        if "Greenberg" in msa_template_path:
            print("✓ Correct Greenberg Traurig template selected")
        else:
            print("✗ Wrong template selected - expected Greenberg Traurig")
            return False
        
        # Test placeholders
        placeholders = dfir.get_placeholders()
        print(f"✓ Generated {len(placeholders)} placeholders")
        
        # Test legal_date placeholder specifically
        with tempfile.TemporaryDirectory() as temp_dir:
            doc_gen = DocumentGenerator(dfir, temp_dir)
            full_placeholders = doc_gen._get_placeholders()
            
            if 'legal_date' in full_placeholders:
                legal_date = full_placeholders['legal_date']
                print(f"✓ Legal date placeholder: {legal_date}")

                # Verify format: "this [day][suffix] day of [month] [year]"
                today = datetime.now()
                expected_day = today.day
                expected_suffix = get_ordinal_suffix(today.day)
                expected_month = today.strftime('%B')
                expected_year = today.year

                if f"{expected_day}{expected_suffix}" in legal_date:
                    print(f"✓ Correct day with ordinal: {expected_day}{expected_suffix}")
                else:
                    print(f"✗ Incorrect day format in legal_date")

                if expected_month in legal_date:
                    print(f"✓ Correct month: {expected_month}")
                else:
                    print(f"✗ Incorrect month in legal_date")

                if str(expected_year) in legal_date:
                    print(f"✓ Correct year: {expected_year}")
                else:
                    print(f"✗ Incorrect year in legal_date")

            else:
                print("✗ Legal date placeholder not found")
                print("Available placeholders with 'date' in name:")
                date_placeholders = {k: v for k, v in full_placeholders.items() if 'date' in k.lower()}
                for key, value in date_placeholders.items():
                    print(f"  {key}: {value}")

                # Let's manually create the legal_date to test the format
                today = datetime.now()
                day_ordinal = get_ordinal_suffix(today.day)
                manual_legal_date = f"this {today.day}{day_ordinal} day of {today.strftime('%B')} {today.year}"
                print(f"Manual legal_date would be: {manual_legal_date}")

                # Don't return False here, continue with other tests
        
        # Test font consistency (RichText vs plain strings)
        print("\nFont Consistency Test:")
        print("-" * 25)
        
        # Check if Greenberg template uses plain strings (not RichText)
        client_placeholder = placeholders.get('client', '')
        law_firm_placeholder = placeholders.get('law_firm', '')
        
        # For Greenberg, these should be plain strings, not RichText objects
        client_type = type(client_placeholder).__name__
        law_firm_type = type(law_firm_placeholder).__name__
        
        print(f"Client placeholder type: {client_type}")
        print(f"Law firm placeholder type: {law_firm_type}")
        
        if client_type == 'str' and law_firm_type == 'str':
            print("✓ Using plain strings for Greenberg template (preserves font consistency)")
        elif client_type == 'RichText' or law_firm_type == 'RichText':
            print("✗ Using RichText objects - may cause font inconsistency in Greenberg template")
            return False
        else:
            print(f"? Unexpected types: client={client_type}, law_firm={law_firm_type}")
        
        # Test ampersand preservation in plain string mode
        if '&' in client.name and '&' in client_placeholder:
            print("✓ Ampersand preserved in client name (plain string mode)")
        elif '&' in client.name:
            print("✗ Ampersand not preserved in client name")
        
        # Test other important placeholders
        important_placeholders = [
            'formatted_date', 'date', 'contact_name', 'contact_email', 'contact_phone'
        ]
        
        print("\nOther Important Placeholders:")
        print("-" * 30)
        for key in important_placeholders:
            if key in full_placeholders:
                value = full_placeholders[key]
                if isinstance(value, str) and len(value) > 50:
                    print(f"✓ {key}: {value[:50]}...")
                else:
                    print(f"✓ {key}: {value}")
            else:
                print(f"✗ {key}: Missing")
        
        # Test actual document generation
        print("\nDocument Generation Test:")
        print("-" * 30)

        try:
            # Check if template exists
            template_path = dfir.get_msa_template_path()
            if os.path.exists(template_path):
                print(f"✓ Template exists: {template_path}")

                # Generate the MSA document
                msa_path = doc_gen.generate_msa()
                if msa_path and os.path.exists(msa_path):
                    print(f"✓ MSA document generated: {os.path.basename(msa_path)}")
                    print(f"  Full path: {msa_path}")

                    # Check file size (should be > 0)
                    file_size = os.path.getsize(msa_path)
                    if file_size > 0:
                        print(f"✓ Document has content: {file_size:,} bytes")
                    else:
                        print("✗ Document is empty")

                else:
                    print("✗ Failed to generate MSA document")
            else:
                print(f"✗ Template not found: {template_path}")

        except Exception as e:
            print(f"✗ Error generating document: {e}")

        print("\n" + "=" * 50)
        print("GREENBERG TRAURIG MSA TEST COMPLETE")
        print("=" * 50)

        return True
        
    except Exception as e:
        print(f"✗ Error during test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_greenberg_msa()
    if success:
        print("✓ All tests passed!")
    else:
        print("✗ Some tests failed!")
