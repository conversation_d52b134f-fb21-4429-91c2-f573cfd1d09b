# PACE v1.1.4 - Team Distribution Guide

## 🎯 **Quick Start - One Command Solution**

### **For Immediate Team Distribution:**
1. **Run**: `build_for_team.bat`
2. **Send**: `team_distribution\PACE.exe` to your team
3. **Done**: Your team can use PACE immediately!

---

## 📦 **Available Build Scripts**

### **1. `build_for_team.bat` (Recommended)**
**Best for**: Quick team distribution
- ✅ Builds executable
- ✅ Creates simple distribution package
- ✅ Includes user instructions
- ✅ Tests the executable
- ⏱️ **Time**: ~2 minutes

**Output**: `team_distribution\PACE.exe` (63 MB)

### **2. `build_complete.bat` (Advanced)**
**Best for**: Professional deployment
- ✅ Builds executable
- ✅ Checks for Inno Setup
- ✅ Creates professional installer (if Inno Setup available)
- ✅ Creates comprehensive distribution package
- ⏱️ **Time**: ~3-5 minutes

**Output**: 
- `distribution\PACE.exe` (63 MB)
- `distribution\PACE_v1.1.4_Setup.exe` (if Inno Setup installed)

### **3. `build_auto_installer.bat` (Experimental)**
**Best for**: Automatic installer creation
- ✅ Builds executable
- ✅ Downloads and installs Inno Setup automatically
- ✅ Creates professional installer
- ⚠️ **Note**: May require admin privileges
- ⏱️ **Time**: ~5-10 minutes

---

## 🚀 **Distribution Methods**

### **Method 1: Single File Distribution (Easiest)**
```
1. Run: build_for_team.bat
2. Send: team_distribution\PACE.exe
3. Instructions: "Double-click PACE.exe to run"
```

**Pros:**
- ✅ Single 63 MB file
- ✅ No installation required
- ✅ Works immediately
- ✅ Easy to email or share

**Cons:**
- ⚠️ Large file for email (use cloud storage if needed)

### **Method 2: Professional Installer**
```
1. Install Inno Setup: https://jrsoftware.org/isinfo.php
2. Run: build_complete.bat
3. Send: distribution\PACE_v1.1.4_Setup.exe
4. Instructions: "Run the installer and follow the wizard"
```

**Pros:**
- ✅ Professional installation experience
- ✅ Creates Start Menu shortcuts
- ✅ Includes uninstaller
- ✅ Familiar to end users

**Cons:**
- ⚠️ Requires Inno Setup installation first
- ⚠️ More complex setup

### **Method 3: Network Share**
```
1. Run: build_for_team.bat
2. Copy: team_distribution\ folder to network share
3. Instructions: "Run PACE.exe from \\server\share\PACE\"
```

**Pros:**
- ✅ Central deployment
- ✅ Easy updates
- ✅ No individual installations

**Cons:**
- ⚠️ Requires network access
- ⚠️ May be slower to start

---

## 📋 **Team Instructions Template**

### **Email Template for Your Team:**

```
Subject: PACE v1.1.4 - Document Generation Tool Now Available

Hi Team,

PACE (Process Automation for Client Engagements) v1.1.4 is now ready for use!

WHAT IS PACE?
- Document generation tool for SOWs, MSAs, BAAs, and DPAs
- Supports DFIR, TACI, BEC, and Recovery & Remediation engagements
- No technical setup required

HOW TO USE:
1. Download the attached PACE.exe file
2. Double-click PACE.exe to run
3. Start creating documents immediately!

SYSTEM REQUIREMENTS:
- Windows 10 or later
- No other software needed

FEATURES:
✓ DFIR SOWs
✓ TACI SOWs  
✓ BEC SOWs
✓ Recovery & Remediation SOWs
✓ Master Service Agreements (MSAs)
✓ Business Associate Agreements (BAAs)
✓ Custom Data Processing Agreements (DPAs)

SUPPORT:
If you have any questions or issues, please contact [Your Name/Team].

Best regards,
[Your Name]
```

---

## 🔧 **Troubleshooting**

### **Build Issues**
- **Python not found**: Install Python 3.9+ from python.org
- **Build fails**: Check that all files are present in PACE directory
- **Permission denied**: Close any running PACE.exe processes

### **Distribution Issues**
- **File too large for email**: Use Google Drive, OneDrive, or Dropbox
- **Antivirus blocks file**: Add exception or use installer method
- **Won't run on target machine**: Ensure Windows 10+ and try "Run as Administrator"

### **Team Usage Issues**
- **Application won't start**: Try running as administrator
- **Templates missing**: Ensure PACE.exe wasn't extracted from a zip incorrectly
- **Documents won't generate**: Check file permissions in output directory

---

## 📊 **File Sizes & Requirements**

| File | Size | Requirements |
|------|------|-------------|
| PACE.exe | ~63 MB | Windows 10+ |
| PACE_v1.1.4_Setup.exe | ~63 MB | Windows 10+ |
| Source Code | ~50 MB | Python 3.9+ |

---

## 🎯 **Recommended Workflow**

### **For Small Teams (< 10 people):**
1. Use `build_for_team.bat`
2. Share `PACE.exe` via cloud storage
3. Provide simple "double-click to run" instructions

### **For Large Teams (10+ people):**
1. Install Inno Setup
2. Use `build_complete.bat`
3. Deploy `PACE_v1.1.4_Setup.exe` via IT systems
4. Provide professional installation instructions

### **For Technical Teams:**
1. Share the entire repository
2. Let them build their own executable
3. Provide build instructions

---

## ✅ **Success Checklist**

Before distributing to your team:

- [ ] Executable builds successfully
- [ ] PACE.exe runs on your machine
- [ ] Can create and generate documents
- [ ] All templates are working
- [ ] File size is reasonable (~63 MB)
- [ ] Instructions are clear
- [ ] Distribution method is chosen
- [ ] Team has been notified

---

## 📞 **Support**

If you need help with the build process or distribution:

1. Check the error messages in the build output
2. Ensure all dependencies are installed
3. Try running as administrator
4. Check the troubleshooting section above
5. Contact the development team if issues persist

**Remember**: The goal is to make PACE as easy as possible for your team to use!
