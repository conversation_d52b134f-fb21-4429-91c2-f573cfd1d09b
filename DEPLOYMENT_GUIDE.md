# PACE v1.1.4 - Deployment Guide

This guide explains how to create a standalone executable and installer for PACE that can run on any Windows machine without requiring Python installation.

## Quick Start - One-Click Build

### Option 1: Use the Batch File (Easiest)
1. **Double-click** `build_pace.bat`
2. **Wait** for the build to complete
3. **Find** your executable at `dist/PACE.exe`

### Option 2: Manual Build
1. **Open Command Prompt** in the PACE directory
2. **Run**: `python build_executable.py`
3. **Test**: `dist/PACE.exe`

## Creating the Installer

### Prerequisites
- **Inno Setup** (free): Download from [jrsoftware.org/isinfo.php](https://jrsoftware.org/isinfo.php)

### Steps
1. **Install Inno Setup** (one-time setup)
2. **Right-click** `PACE_installer.iss`
3. **Select** "Compile" from the context menu
4. **Find installer** at: `installer/PACE_v1.1.4_Setup.exe`

## Deployment Features

### Standalone Executable (`PACE.exe`)
-  **No Python required** - Runs on any Windows 10/11 machine
-  **All dependencies bundled** - PySide6, docxtpl, templates, etc.
-  **Single file** - Easy to distribute
-  **~50-100MB** - Reasonable size for distribution

### Professional Installer (`PACE_v1.1.4_Setup.exe`)
-  **One-click installation** - Standard Windows installer experience
-  **Desktop shortcut** - Optional desktop icon creation
-  **Start menu entry** - Appears in Windows Start menu
-  **Automatic updates** - Handles upgrading from previous versions
-  **Clean uninstall** - Proper Windows uninstaller
-  **No admin required** - Installs to user directory by default
-  **Admin option** - Can install system-wide if admin rights available

## Administrative Privileges Handling

### User Installation (Default)
- **Location**: `%USERPROFILE%/AppData/Local/Programs/PACE/`
- **Privileges**: No admin rights required
- **Scope**: Current user only
- **Recommended for**: Individual users, restricted environments

### System Installation (Optional)
- **Location**: `C:/Program Files/PACE/`
- **Privileges**: Admin rights required
- **Scope**: All users on the machine
- **Recommended for**: IT deployments, shared computers

### Smart Privilege Detection
The installer automatically:
1. **Tries user installation** first (no admin needed)
2. **Offers system installation** if admin rights detected
3. **Gracefully handles** restricted environments
4. **Provides clear feedback** about installation location

## File Structure After Build

```
PACE/
├── dist/
│   └── PACE.exe                    # Standalone executable
├── installer/
│   └── PACE_v1.1.4_Setup.exe     # Professional installer
├── build/                          # Build artifacts (can delete)
├── PACE.spec                       # PyInstaller configuration
├── PACE_installer.iss              # Inno Setup script
├── version_info.txt                # Windows version info
├── LICENSE.txt                     # License file
└── build_pace.bat                  # One-click build script
```

## Distribution Options

### Option 1: Standalone Executable
**Best for**: Quick testing, portable use, technical users
- **File**: `dist/PACE.exe`
- **Size**: ~50-100MB
- **Installation**: None required - just run
- **Distribution**: Email, USB drive, network share

### Option 2: Professional Installer
**Best for**: End users, IT deployment, production use
- **File**: `installer/PACE_v1.1.4_Setup.exe`
- **Size**: ~50-100MB
- **Installation**: Standard Windows installer
- **Distribution**: Download portal, IT deployment tools

### Option 3: Portable Package
**Best for**: Multiple machines, backup distribution
- **Create**: Zip the entire `dist/` folder
- **Include**: PACE.exe + README.md + CHANGELOG.md
- **Distribution**: Archive file for manual extraction

## Testing Your Build

### Basic Functionality Test
1. **Run** `dist/PACE.exe`
2. **Create** a test engagement
3. **Generate** documents
4. **Verify** all features work

### Installation Test
1. **Run** `PACE_v1.1.4_Setup.exe`
2. **Follow** installation wizard
3. **Launch** from Start menu or desktop
4. **Test** full functionality

### Compatibility Test
- **Windows 10** (minimum supported)
- **Windows 11** (recommended)
- **Different user accounts** (admin vs standard)
- **Network drives** (if templates stored remotely)

## Troubleshooting

### Build Issues
- **PyInstaller not found**: Run `pip install pyinstaller`
- **Missing dependencies**: Run `pip install -r requirements.txt`
- **Large executable size**: Normal for bundled applications
- **Antivirus warnings**: Common with PyInstaller - add exception

### Runtime Issues
- **Slow startup**: Normal for first run (Windows security scan)
- **Missing templates**: Ensure templates folder included in build
- **Font issues**: Include system fonts if custom fonts used
- **Permission errors**: Run as administrator or use user installation

### Installer Issues
- **Inno Setup not found**: Download from official website
- **Compilation errors**: Check PACE_installer.iss syntax
- **Installation fails**: Try running installer as administrator
- **Upgrade issues**: Uninstall previous version first

## Customization Options

### Branding
- **Icon**: Replace `PACE.ico` with your company icon
- **Company name**: Edit `version_info.txt` and `PACE_installer.iss`
- **URLs**: Update support/website URLs in installer script

### Installation Behavior
- **Default location**: Modify `DefaultDirName` in installer script
- **Required privileges**: Change `PrivilegesRequired` setting
- **Desktop icon**: Modify `desktopicon` task settings

### Executable Options
- **Console mode**: Set `console=True` in PACE.spec for debugging
- **Compression**: Adjust `upx=True/False` for size vs speed
- **Hidden imports**: Add modules to `hiddenimports` if missing

## Production Deployment Checklist

- [ ] **Test executable** on clean Windows machine
- [ ] **Verify all templates** are included and accessible
- [ ] **Test installer** with both user and admin privileges
- [ ] **Check antivirus** compatibility (whitelist if needed)
- [ ] **Document installation** process for end users
- [ ] **Prepare support** documentation and contact info
- [ ] **Test upgrade** process from previous versions
- [ ] **Backup original** source code and build environment

## Automated Build Pipeline (Future)

For continuous deployment, consider:
- **GitHub Actions** for automated builds
- **Code signing** for trusted executables
- **Automatic updates** via web service
- **Telemetry** for usage analytics
- **Crash reporting** for error tracking

---

**PACE v1.1.4** - Ready for enterprise deployment with professional installation experience.
