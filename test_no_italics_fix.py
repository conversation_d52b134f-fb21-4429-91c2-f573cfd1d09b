#!/usr/bin/env python3
"""
Test the Greenberg MSA with the no-italics fix.
"""

import os
import tempfile
from datetime import datetime

def test_no_italics_fix():
    """Test Greenberg MSA with explicit no-italics fix"""
    print("TESTING GREENBERG MSA WITH NO-ITALICS FIX")
    print("=" * 50)
    
    try:
        from models.dfir import DFIREngagement
        from models.client import Client
        from utils.document_generator import DocumentGenerator
        
        # Create test client with <PERSON> Traurig
        client = Client()
        client.name = "No Italics Test Corp"
        client.address = "789 Regular Font Ave, Suite 300, Chicago, IL 60601"
        client.law_firm = "<PERSON> Traurig, LLP"
        client.insurance_carrier = "Travelers"
        
        # Create engagement
        engagement = DFIREngagement()
        engagement.client = client
        engagement.contact_name = "Regular Font User"
        engagement.contact_email = "<EMAIL>"
        engagement.contact_phone = "************"
        engagement.engagement_type = "Digital Forensics"
        engagement.endpoint_count = 50
        engagement.edr_monitoring = "Monitoring and Threat Hunting New Console"
        
        print(f"✓ Test setup complete:")
        print(f"  - Client: {client.name}")
        print(f"  - Law Firm: {client.law_firm}")
        
        # Generate MSA document
        with tempfile.TemporaryDirectory() as temp_dir:
            doc_gen = DocumentGenerator(engagement, temp_dir)
            
            print(f"\n📁 Output directory: {temp_dir}")
            
            # Generate MSA
            print("\n🔄 Generating MSA with no-italics fix...")
            msa_path = doc_gen.generate_msa()
            
            if msa_path and os.path.exists(msa_path):
                print(f"✅ MSA generated successfully!")
                print(f"📄 Path: {msa_path}")
                
                # Copy to current directory for inspection
                import shutil
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_path = f"Greenberg_MSA_NoItalics_Test_{timestamp}.docx"
                shutil.copy2(msa_path, output_path)
                print(f"💾 Test document saved as: {output_path}")
                
                # Analyze the document formatting
                print("\n🔍 ANALYZING FORMATTING...")
                analyze_formatting(output_path)
                
                return True, output_path
            else:
                print("❌ Failed to generate MSA")
                return False, None
                
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def analyze_formatting(docx_path):
    """Analyze formatting in the document"""
    try:
        from docx import Document
        
        doc = Document(docx_path)
        
        # Check the first paragraph specifically
        first_paragraph = doc.paragraphs[0]
        print(f"📝 FIRST PARAGRAPH ANALYSIS:")
        print(f"  Text: {first_paragraph.text[:80]}...")
        
        italic_count = 0
        total_runs = 0
        
        # Check all runs in the first paragraph
        for run in first_paragraph.runs:
            if run.text.strip():
                total_runs += 1
                if run.font.italic is True:
                    italic_count += 1
                    print(f"  ⚠️  ITALIC FOUND: '{run.text[:30]}...'")
        
        print(f"  Total runs: {total_runs}")
        print(f"  Italic runs: {italic_count}")
        
        if italic_count == 0:
            print(f"  ✅ NO ITALICS DETECTED in first paragraph!")
        else:
            print(f"  ❌ {italic_count} italic runs found!")
        
        # Check fonts
        fonts_found = set()
        for run in first_paragraph.runs:
            if run.font.name:
                fonts_found.add(run.font.name)
        
        print(f"  Fonts used: {sorted(fonts_found)}")
        
        return italic_count == 0
        
    except Exception as e:
        print(f"❌ Error analyzing formatting: {e}")
        return False

def main():
    """Main test function"""
    print("Starting no-italics fix test...\n")
    
    success, output_file = test_no_italics_fix()
    
    print("\n" + "=" * 50)
    print("NO-ITALICS FIX TEST SUMMARY")
    print("=" * 50)
    
    if success:
        print("✅ No-italics fix test PASSED")
        print(f"📄 Test file: {output_file}")
        print("\n📋 VERIFICATION STEPS:")
        print("1. Open the generated document")
        print("2. Verify that NO text is italicized")
        print("3. Check that Garamond font is used consistently")
        print("4. Confirm the document looks like the original template")
    else:
        print("❌ No-italics fix test FAILED")
        print("Please review the errors above.")
    
    print("\n" + "=" * 50)

if __name__ == "__main__":
    main()
