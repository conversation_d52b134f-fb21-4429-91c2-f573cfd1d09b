@echo off
echo PACE v1.1.4 - One-Click Build Script
echo =====================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python not found. Please install Python 3.9+ first.
    echo Download from: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo Python found. Building PACE executable...
echo.

REM Run the build script
python build_executable.py

if errorlevel 1 (
    echo.
    echo Build failed! Check the error messages above.
    pause
    exit /b 1
)

echo.
echo =====================================
echo Build completed successfully!
echo =====================================
echo.
echo Files created:
echo - dist\PACE.exe (Standalone executable)
echo - PACE_installer.iss (Installer script)
echo.
echo To create installer:
echo 1. Download Inno Setup: https://jrsoftware.org/isinfo.php
echo 2. Right-click PACE_installer.iss and select "Compile"
echo 3. Find installer in: installer\PACE_v1.1.4_Setup.exe
echo.
pause
