@echo off
setlocal enabledelayedexpansion

echo =====================================
echo PACE v1.1.4 - Complete Build Script
echo =====================================
echo This script will:
echo 1. Build the PACE executable
echo 2. Check for Inno Setup
echo 3. Create the installer automatically
echo 4. Package everything for distribution
echo.

REM Check if we're in the right directory
if not exist "main.py" (
    echo Error: main.py not found!
    echo Please run this script from the PACE root directory.
    pause
    exit /b 1
)

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python not found. Please install Python 3.9+ first.
    echo Download from: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✓ Python found
echo.

echo [1/4] Building PACE executable...
echo =====================================

REM Run the build script
python build_executable.py

REM Check if build was successful
if not exist "dist\PACE.exe" (
    echo.
    echo ✗ Build FAILED!
    echo Please check the error messages above.
    pause
    exit /b 1
)

for %%A in ("dist\PACE.exe") do set "EXE_SIZE=%%~zA"
set /a "EXE_SIZE_MB=!EXE_SIZE! / 1048576"
echo ✓ PACE.exe created successfully (Size: !EXE_SIZE_MB! MB)
echo.

echo [2/4] Checking for Inno Setup...
echo =====================================

REM Check if Inno Setup is installed
set "INNO_FOUND=0"
set "ISCC_PATH="

REM Check common installation paths
if exist "C:\Program Files (x86)\Inno Setup 6\iscc.exe" (
    set "ISCC_PATH=C:\Program Files (x86)\Inno Setup 6\iscc.exe"
    set "INNO_FOUND=1"
) else if exist "C:\Program Files\Inno Setup 6\iscc.exe" (
    set "ISCC_PATH=C:\Program Files\Inno Setup 6\iscc.exe"
    set "INNO_FOUND=1"
) else if exist "C:\Program Files (x86)\Inno Setup 5\iscc.exe" (
    set "ISCC_PATH=C:\Program Files (x86)\Inno Setup 5\iscc.exe"
    set "INNO_FOUND=1"
) else if exist "C:\Program Files\Inno Setup 5\iscc.exe" (
    set "ISCC_PATH=C:\Program Files\Inno Setup 5\iscc.exe"
    set "INNO_FOUND=1"
) else (
    REM Try to find iscc.exe in PATH
    where iscc.exe >nul 2>&1
    if !errorlevel! equ 0 (
        for /f "tokens=*" %%i in ('where iscc.exe') do set "ISCC_PATH=%%i"
        set "INNO_FOUND=1"
    )
)

if !INNO_FOUND! equ 1 (
    echo ✓ Inno Setup found at: !ISCC_PATH!
    echo.
    echo [3/4] Creating installer...
    echo =====================================
    
    REM Create installer directory if it doesn't exist
    if not exist "installer" mkdir installer
    
    REM Compile the installer
    echo Compiling installer with Inno Setup...
    "!ISCC_PATH!" PACE_installer.iss
    
    if exist "installer\PACE_v1.1.4_Setup.exe" (
        for %%A in ("installer\PACE_v1.1.4_Setup.exe") do set "INSTALLER_SIZE=%%~zA"
        set /a "INSTALLER_SIZE_MB=!INSTALLER_SIZE! / 1048576"
        echo ✓ Installer created successfully (Size: !INSTALLER_SIZE_MB! MB)
        set "INSTALLER_CREATED=1"
    ) else (
        echo ✗ Installer creation failed!
        echo Check the Inno Setup compilation output above for errors.
        set "INSTALLER_CREATED=0"
    )
) else (
    echo ✗ Inno Setup not found!
    echo.
    echo Inno Setup is required to create the installer.
    echo Would you like to download and install it now? (y/n)
    set /p "INSTALL_INNO="
    
    if /i "!INSTALL_INNO!"=="y" (
        echo.
        echo Opening Inno Setup download page...
        start https://jrsoftware.org/isinfo.php
        echo.
        echo Please:
        echo 1. Download and install Inno Setup (it's free)
        echo 2. Run this script again to create the installer
        echo.
        set "INSTALLER_CREATED=0"
    ) else (
        echo.
        echo Skipping installer creation.
        set "INSTALLER_CREATED=0"
    )
)

echo.
echo [4/4] Creating distribution package...
echo =====================================

REM Create distribution folder
if not exist "distribution" mkdir distribution

REM Copy standalone executable
copy "dist\PACE.exe" "distribution\" >nul
echo ✓ Copied standalone executable

REM Copy documentation
copy "README.md" "distribution\" >nul 2>&1
copy "CHANGELOG.md" "distribution\" >nul 2>&1
copy "LICENSE.txt" "distribution\" >nul 2>&1
echo ✓ Copied documentation

REM Copy installer if it was created
if !INSTALLER_CREATED! equ 1 (
    copy "installer\PACE_v1.1.4_Setup.exe" "distribution\" >nul
    echo ✓ Copied installer
)

REM Create distribution info file
echo PACE v1.1.4 - Distribution Package > "distribution\DISTRIBUTION_INFO.txt"
echo ======================================= >> "distribution\DISTRIBUTION_INFO.txt"
echo. >> "distribution\DISTRIBUTION_INFO.txt"
echo This package contains: >> "distribution\DISTRIBUTION_INFO.txt"
echo. >> "distribution\DISTRIBUTION_INFO.txt"
echo 1. PACE.exe - Standalone executable >> "distribution\DISTRIBUTION_INFO.txt"
echo    - No installation required >> "distribution\DISTRIBUTION_INFO.txt"
echo    - Just run PACE.exe >> "distribution\DISTRIBUTION_INFO.txt"
echo    - Size: !EXE_SIZE_MB! MB >> "distribution\DISTRIBUTION_INFO.txt"
echo. >> "distribution\DISTRIBUTION_INFO.txt"

if !INSTALLER_CREATED! equ 1 (
    echo 2. PACE_v1.1.4_Setup.exe - Professional installer >> "distribution\DISTRIBUTION_INFO.txt"
    echo    - Standard Windows installation >> "distribution\DISTRIBUTION_INFO.txt"
    echo    - Creates Start Menu shortcuts >> "distribution\DISTRIBUTION_INFO.txt"
    echo    - Size: !INSTALLER_SIZE_MB! MB >> "distribution\DISTRIBUTION_INFO.txt"
    echo. >> "distribution\DISTRIBUTION_INFO.txt"
)

echo 3. Documentation >> "distribution\DISTRIBUTION_INFO.txt"
echo    - README.md - User guide >> "distribution\DISTRIBUTION_INFO.txt"
echo    - CHANGELOG.md - Version history >> "distribution\DISTRIBUTION_INFO.txt"
echo    - LICENSE.txt - License information >> "distribution\DISTRIBUTION_INFO.txt"
echo. >> "distribution\DISTRIBUTION_INFO.txt"
echo System Requirements: >> "distribution\DISTRIBUTION_INFO.txt"
echo - Windows 10 or later >> "distribution\DISTRIBUTION_INFO.txt"
echo - No Python installation required >> "distribution\DISTRIBUTION_INFO.txt"
echo - No additional dependencies needed >> "distribution\DISTRIBUTION_INFO.txt"

echo ✓ Created distribution info file
echo.

echo =====================================
echo BUILD COMPLETE!
echo =====================================
echo.
echo ✓ Standalone executable: dist\PACE.exe (!EXE_SIZE_MB! MB)

if !INSTALLER_CREATED! equ 1 (
    echo ✓ Professional installer: installer\PACE_v1.1.4_Setup.exe (!INSTALLER_SIZE_MB! MB)
) else (
    echo ⚠ Professional installer: Not created (Inno Setup required)
)

echo ✓ Distribution package: distribution\ folder
echo.
echo DISTRIBUTION OPTIONS:
echo =====================================
echo.
echo Option 1: Standalone Distribution
echo   File: distribution\PACE.exe
echo   Use: Send this single file to your team
echo   Install: None required - just run
echo.

if !INSTALLER_CREATED! equ 1 (
    echo Option 2: Professional Installation
    echo   File: distribution\PACE_v1.1.4_Setup.exe
    echo   Use: Professional deployment
    echo   Install: Standard Windows installer
    echo.
    echo Option 3: Complete Package
    echo   Folder: distribution\
    echo   Use: Zip the entire folder for complete package
    echo   Contains: Both executable and installer plus docs
) else (
    echo Option 2: Manual Installer Creation
    echo   1. Install Inno Setup from: https://jrsoftware.org/isinfo.php
    echo   2. Right-click PACE_installer.iss and select "Compile"
    echo   3. Find installer in: installer\PACE_v1.1.4_Setup.exe
)

echo.
echo NEXT STEPS:
echo =====================================
echo 1. Test: Run distribution\PACE.exe to verify it works
echo 2. Distribute: Send the file(s) to your team
echo 3. Deploy: Your team can run the executable immediately
echo.

if !INSTALLER_CREATED! equ 0 (
    echo NOTE: To create the professional installer later:
    echo 1. Install Inno Setup (free): https://jrsoftware.org/isinfo.php
    echo 2. Run this script again
    echo.
)

echo Press any key to open the distribution folder...
pause >nul
explorer "distribution"
