#!/usr/bin/env python3
"""
Build script to create standalone executable for PACE v1.1.4
Creates a single-file executable with all dependencies bundled.
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def install_pyinstaller():
    """Install PyInstaller if not already installed"""
    try:
        import PyInstaller
        print("✓ PyInstaller already installed")
        return True
    except ImportError:
        print("Installing PyInstaller...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print("✓ PyInstaller installed successfully")
            return True
        except subprocess.CalledProcessError as e:
            print(f"✗ Failed to install PyInstaller: {e}")
            return False

def create_spec_file():
    """Create PyInstaller spec file for PACE"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# Define the main script and data files
a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('templates', 'templates'),
        ('data', 'data'),
        ('resources', 'resources'),
        ('README.md', '.'),
        ('CHANGELOG.md', '.'),
        ('requirements.txt', '.'),
    ],
    hiddenimports=[
        'PySide6.QtCore',
        'PySide6.QtWidgets',
        'PySide6.QtGui',
        'docxtpl',
        'python-docx',
        'docxcompose',
        'jinja2',
        'lxml',
        'PIL',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='PACE',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # Set to False for GUI application
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='PACE.ico',  # Application icon
    version='version_info.txt'  # Version information
)
'''
    
    with open('PACE.spec', 'w') as f:
        f.write(spec_content)
    print("✓ Created PACE.spec file")

def create_version_info():
    """Create version information file for Windows executable"""
    version_info = '''# UTF-8
#
# For more details about fixed file info 'ffi' see:
# http://msdn.microsoft.com/en-us/library/ms646997.aspx
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1, 1, 4, 0),
    prodvers=(1, 1, 4, 0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'040904B0',
        [StringStruct(u'CompanyName', u'Your Company'),
        StringStruct(u'FileDescription', u'PACE - Process Automation for Client Engagements'),
        StringStruct(u'FileVersion', u'*******'),
        StringStruct(u'InternalName', u'PACE'),
        StringStruct(u'LegalCopyright', u'Copyright (C) 2025 Your Company'),
        StringStruct(u'OriginalFilename', u'PACE.exe'),
        StringStruct(u'ProductName', u'PACE'),
        StringStruct(u'ProductVersion', u'*******')])
      ]), 
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)
'''
    
    with open('version_info.txt', 'w') as f:
        f.write(version_info)
    print("✓ Created version_info.txt file")

def build_executable():
    """Build the standalone executable"""
    print("Building PACE executable...")

    # Check if icon file exists
    if not os.path.exists('PACE.ico'):
        print("❌ Error: PACE.ico not found!")
        print("   Please place your custom PACE.ico file in the root directory before building.")
        return False
    else:
        print("✓ PACE.ico found")

    try:
        # Clean previous builds
        if os.path.exists('dist'):
            shutil.rmtree('dist')
        if os.path.exists('build'):
            shutil.rmtree('build')
        
        # Build using spec file
        cmd = [sys.executable, "-m", "PyInstaller", "--clean", "PACE.spec"]
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ Executable built successfully!")
            
            # Check if executable was created
            exe_path = Path("dist/PACE.exe")
            if exe_path.exists():
                size_mb = exe_path.stat().st_size / (1024 * 1024)
                print(f"✓ PACE.exe created: {size_mb:.1f} MB")
                return True
            else:
                print("✗ Executable not found in dist folder")
                return False
        else:
            print("✗ Build failed:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"✗ Build error: {e}")
        return False

def create_installer_script():
    """Create Inno Setup installer script"""
    installer_script = '''[Setup]
AppName=PACE
AppVersion=1.1.4
AppPublisher=Your Company
AppPublisherURL=https://yourcompany.com
AppSupportURL=https://yourcompany.com/support
AppUpdatesURL=https://yourcompany.com/updates
DefaultDirName={autopf}\\PACE
DefaultGroupName=PACE
AllowNoIcons=yes
LicenseFile=LICENSE.txt
OutputDir=installer
OutputBaseFilename=PACE_v1.1.4_Setup
SetupIconFile=PACE.ico
Compression=lzma
SolidCompression=yes
WizardStyle=modern
PrivilegesRequired=lowest
PrivilegesRequiredOverridesAllowed=dialog

[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked
Name: "quicklaunchicon"; Description: "{cm:CreateQuickLaunchIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked; OnlyBelowVersion: 6.1

[Files]
Source: "dist\\PACE.exe"; DestDir: "{app}"; Flags: ignoreversion
Source: "README.md"; DestDir: "{app}"; Flags: ignoreversion
Source: "CHANGELOG.md"; DestDir: "{app}"; Flags: ignoreversion
Source: "LICENSE.txt"; DestDir: "{app}"; Flags: ignoreversion isreadme

[Icons]
Name: "{group}\\PACE"; Filename: "{app}\\PACE.exe"
Name: "{group}\\{cm:ProgramOnTheWeb,PACE}"; Filename: "https://yourcompany.com"
Name: "{group}\\{cm:UninstallProgram,PACE}"; Filename: "{uninstallexe}"
Name: "{autodesktop}\\PACE"; Filename: "{app}\\PACE.exe"; Tasks: desktopicon
Name: "{userappdata}\\Microsoft\\Internet Explorer\\Quick Launch\\PACE"; Filename: "{app}\\PACE.exe"; Tasks: quicklaunchicon

[Run]
Filename: "{app}\\PACE.exe"; Description: "{cm:LaunchProgram,PACE}"; Flags: nowait postinstall skipifsilent

[Code]
function GetUninstallString(): String;
var
  sUnInstPath: String;
  sUnInstallString: String;
begin
  sUnInstPath := ExpandConstant('Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{#emit SetupSetting("AppId")}_is1');
  sUnInstallString := '';
  if not RegQueryStringValue(HKLM, sUnInstPath, 'UninstallString', sUnInstallString) then
    RegQueryStringValue(HKCU, sUnInstPath, 'UninstallString', sUnInstallString);
  Result := sUnInstallString;
end;

function IsUpgrade(): Boolean;
begin
  Result := (GetUninstallString() <> '');
end;

function UnInstallOldVersion(): Integer;
var
  sUnInstallString: String;
  iResultCode: Integer;
begin
  Result := 0;
  sUnInstallString := GetUninstallString();
  if sUnInstallString <> '' then begin
    sUnInstallString := RemoveQuotes(sUnInstallString);
    if Exec(sUnInstallString, '/SILENT /NORESTART /SUPPRESSMSGBOXES','', SW_HIDE, ewWaitUntilTerminated, iResultCode) then
      Result := 3
    else
      Result := 2;
  end else
    Result := 1;
end;

procedure CurStepChanged(CurStep: TSetupStep);
begin
  if (CurStep=ssInstall) then
  begin
    if (IsUpgrade()) then
    begin
      UnInstallOldVersion();
    end;
  end;
end;
'''
    
    with open('PACE_installer.iss', 'w') as f:
        f.write(installer_script)
    print("✓ Created PACE_installer.iss file")

def create_license_file():
    """Create a simple license file"""
    license_text = """PACE - Process Automation for Client Engagements
Version 1.1.4

This software is for internal business use only.
All rights reserved.

Copyright (C) 2025 Your Company Name

This software is provided "as is" without warranty of any kind.
"""
    
    with open('LICENSE.txt', 'w') as f:
        f.write(license_text)
    print("✓ Created LICENSE.txt file")

def main():
    """Main build process"""
    print("PACE v1.1.4 - Executable Build Script")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not os.path.exists('main.py'):
        print("✗ Error: main.py not found. Run this script from the PACE root directory.")
        return False
    
    # Install PyInstaller
    if not install_pyinstaller():
        return False
    
    # Create build files
    create_spec_file()
    create_version_info()
    create_license_file()
    create_installer_script()
    
    # Build executable
    if not build_executable():
        return False
    
    print("\n" + "=" * 50)
    print("✓ BUILD COMPLETE!")
    print("=" * 50)
    print(f"Executable: dist/PACE.exe")
    print(f"Installer script: PACE_installer.iss")
    print("\nNext steps:")
    print("1. Test the executable: dist/PACE.exe")
    print("2. Install Inno Setup from: https://jrsoftware.org/isinfo.php")
    print("3. Compile installer: Right-click PACE_installer.iss → Compile")
    print("4. Find installer: installer/PACE_v1.1.4_Setup.exe")
    
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
