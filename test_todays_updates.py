#!/usr/bin/env python3
"""
Optimized test suite for today's PACE updates.
Tests DFIR Phase I project management hours calculation.
"""

# Import at module level for better performance
from data.pricing_tables import get_phase_hours

# Test data - optimized for readability and performance
PHASE1_TEST_CASES = [
    # (endpoints, expected_hours, description)
    (50, 40, "0-100 endpoints"),
    (100, 40, "0-100 endpoints"),
    (250, 50, "101-500 endpoints"),
    (500, 50, "101-500 endpoints"),
    (750, 75, "501-1,000 endpoints"),
    (1000, 75, "501-1,000 endpoints"),
    (1500, 100, "1,001-2,500 endpoints"),
    (2500, 100, "1,001-2,500 endpoints"),
    (3000, 225, "2,501-5,000 endpoints"),
    (5000, 225, "2,501-5,000 endpoints"),
    (7500, 315, "5,001-9,999 endpoints"),
    (10000, 425, "10,000-15,000 endpoints"),
    (15000, 425, "10,000-15,000 endpoints"),
    (20000, 500, "15,001-25,000 endpoints"),
    (25000, 500, "15,001-25,000 endpoints"),
    (35000, 750, "25,001-50,000 endpoints"),
    (50000, 750, "25,001-50,000 endpoints"),
    (75000, 1000, "50,000+ endpoints"),
]

def test_phase1_hours():
    """Test DFIR Phase I project management hours calculation"""
    print("\n\n4. DFIR PHASE I - Project Management Updated Hours")
    print("-" * 55)

    passed = 0
    failed = 0

    for endpoints, expected_hours, _ in PHASE1_TEST_CASES:
        actual_hours = get_phase_hours(1, endpoints)
        if actual_hours == expected_hours:
            status = "✓ PASS"
            passed += 1
        else:
            status = "✗ FAIL"
            failed += 1
        print(f"{endpoints:,} endpoints: {actual_hours}hrs (expected: {expected_hours}hrs) - {status}")

    print(f"\nResults: {passed} passed, {failed} failed")
    return failed == 0

if __name__ == "__main__":
    success = test_phase1_hours()
    exit(0 if success else 1)
















