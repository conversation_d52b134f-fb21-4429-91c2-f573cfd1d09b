def test_phase1_hours():
    """Test DFIR Phase I project management hours calculation"""
    print("\n\n4. DFIR PHASE I - Project Management Updated Hours")
    print("-" * 55)

    phase1_test_cases = [
        (50, 40),      # 0-100 endpoints = 40 hours
        (100, 40),     # 0-100 endpoints = 40 hours
        (250, 50),     # 101-500 endpoints = 50 hours
        (500, 50),     # 101-500 endpoints = 50 hours
        (750, 75),     # 501-1,000 endpoints = 75 hours
        (1000, 75),    # 501-1,000 endpoints = 75 hours
        (1500, 100),   # 1,001-2,500 endpoints = 100 hours
        (2500, 100),   # 1,001-2,500 endpoints = 100 hours
        (3000, 225),   # 2,501-5,000 endpoints = 225 hours
        (5000, 225),   # 2,501-5,000 endpoints = 225 hours
        (7500, 315),   # 5,001-10,000 endpoints = 315 hours
        (10000, 425),  # 10,001-15,000 endpoints = 425 hours
        (15000, 425),  # 10,001-15,000 endpoints = 425 hours
        (20000, 500),  # 15,001-25,000 endpoints = 500 hours
        (25000, 500),  # 15,001-25,000 endpoints = 500 hours
        (35000, 750),  # 25,001-50,000 endpoints = 750 hours
        (50000, 750),  # 25,001-50,000 endpoints = 750 hours
        (75000, 1000), # 50,000+ endpoints = 1,000 hours
    ]

    from data.pricing_tables import get_phase_hours

    for endpoints, expected_hours in phase1_test_cases:
        actual_hours = get_phase_hours(1, endpoints)
        status = "✓ PASS" if actual_hours == expected_hours else "✗ FAIL"
        print(f"{endpoints:,} endpoints: {actual_hours}hrs (expected: {expected_hours}hrs) - {status}")


















