#!/usr/bin/env python3
"""
Check the original Greenberg template formatting to see if it has italics.
"""

from docx import Document

def check_template_formatting():
    """Check the formatting in the original Greenberg template"""
    try:
        template_path = "templates/base_templates/msa_templates/Greenberg_Traurig_LLP_Master_Services_Agreement_Template.docx"
        
        print("CHECKING ORIGINAL GREENBERG TEMPLATE FORMATTING")
        print("=" * 60)
        
        doc = Document(template_path)
        
        # Check the first few paragraphs
        for i, paragraph in enumerate(doc.paragraphs[:5]):
            if paragraph.text.strip():  # Only check non-empty paragraphs
                print(f"\nPARAGRAPH {i+1}:")
                print(f"Text: {paragraph.text[:100]}...")
                
                # Check each run in the paragraph
                for j, run in enumerate(paragraph.runs):
                    if run.text.strip():
                        print(f"  Run {j+1}:")
                        print(f"    Text: '{run.text[:50]}...'")
                        print(f"    Font: {run.font.name}")
                        print(f"    Size: {run.font.size}")
                        print(f"    Bold: {run.font.bold}")
                        print(f"    Italic: {run.font.italic}")
                        print(f"    Underline: {run.font.underline}")
                
                if i >= 2:  # Only check first 3 paragraphs
                    break
        
        return True
        
    except Exception as e:
        print(f"Error: {e}")
        return False

if __name__ == "__main__":
    check_template_formatting()
