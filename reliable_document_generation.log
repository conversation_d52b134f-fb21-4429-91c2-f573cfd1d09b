2025-07-16 15:34:39,266 - root - INFO - Starting PACE application
2025-07-16 15:34:39,266 - root - INFO - Python version: 3.12.7 (tags/v3.12.7:0b05ead, Oct  1 2024, 03:06:41) [MSC v.1941 64 bit (AMD64)]
2025-07-16 15:34:39,267 - root - INFO - Current directory: D:\PACE_1.1.3
2025-07-16 15:34:39,267 - root - INFO - Script directory: D:\PACE_1.1.3
2025-07-16 15:34:39,267 - root - INFO - Executable directory: D:\PACE_1.1.3\venv\Scripts
2025-07-16 15:34:39,508 - root - INFO - Checking for icon at: PACE.ico (exists: True)
2025-07-16 15:34:39,508 - root - INFO - Checking for icon at: D:\PACE_1.1.3\PACE.ico (exists: True)
2025-07-16 15:34:39,509 - root - INFO - Checking for icon at: D:\PACE_1.1.3\venv\Scripts\PACE.ico (exists: False)
2025-07-16 15:34:39,512 - root - INFO - Checking for icon at: app_icon.ico (exists: False)
2025-07-16 15:34:39,512 - root - INFO - Checking for icon at: D:\PACE_1.1.3\app_icon.ico (exists: False)
2025-07-16 15:34:39,513 - root - INFO - Checking for icon at: D:\PACE_1.1.3\venv\Scripts\app_icon.ico (exists: False)
2025-07-16 15:34:40,473 - root - INFO - Found and loaded icon from: PACE.ico
2025-07-16 15:34:43,640 - root - INFO - Main window created and shown successfully
2025-07-16 17:18:39,208 - root - INFO - Starting PACE application
2025-07-16 17:18:39,208 - root - INFO - Python version: 3.12.7 (tags/v3.12.7:0b05ead, Oct  1 2024, 03:06:41) [MSC v.1941 64 bit (AMD64)]
2025-07-16 17:18:39,209 - root - INFO - Current directory: D:\PACE_1.1.3
2025-07-16 17:18:39,209 - root - INFO - Script directory: D:\PACE_1.1.3
2025-07-16 17:18:39,209 - root - INFO - Executable directory: D:\PACE_1.1.3\venv\Scripts
2025-07-16 17:18:39,245 - root - INFO - Checking for icon at: PACE.ico (exists: True)
2025-07-16 17:18:39,246 - root - INFO - Checking for icon at: D:\PACE_1.1.3\PACE.ico (exists: True)
2025-07-16 17:18:39,246 - root - INFO - Checking for icon at: D:\PACE_1.1.3\venv\Scripts\PACE.ico (exists: False)
2025-07-16 17:18:39,247 - root - INFO - Checking for icon at: app_icon.ico (exists: False)
2025-07-16 17:18:39,247 - root - INFO - Checking for icon at: D:\PACE_1.1.3\app_icon.ico (exists: False)
2025-07-16 17:18:39,247 - root - INFO - Checking for icon at: D:\PACE_1.1.3\venv\Scripts\app_icon.ico (exists: False)
2025-07-16 17:18:39,336 - root - INFO - Found and loaded icon from: PACE.ico
2025-07-16 17:18:40,003 - root - INFO - Main window created and shown successfully
2025-07-16 17:19:58,167 - utils.reliable_document_generator - INFO - Using output directory: C:/Users/<USER>/Documents/Test_Pace
2025-07-16 17:19:58,167 - utils.reliable_document_generator - INFO - ReliableDocumentGenerator initialized with output directory: C:/Users/<USER>/Documents/Test_Pace
2025-07-16 17:19:58,168 - utils.reliable_document_generator - INFO - Debugging template paths...
2025-07-16 17:19:58,168 - utils.reliable_document_generator - INFO - SOW template path: templates/base_templates/dfir/SOW_IR_Template_single_price_IR_Investigation.docx
2025-07-16 17:19:58,168 - utils.reliable_document_generator - INFO - Resolved SOW template path: templates/base_templates/dfir/SOW_IR_Template_single_price_IR_Investigation.docx
2025-07-16 17:19:58,168 - utils.reliable_document_generator - INFO - SOW template exists: True
2025-07-16 17:19:58,168 - utils.reliable_document_generator - INFO - MSA template path: templates/base_templates/msa_templates/Greenberg_Traurig_LLP_Master_Services_Agreement_Template.docx
2025-07-16 17:19:58,169 - utils.reliable_document_generator - INFO - Resolved MSA template path: templates/base_templates/msa_templates/Greenberg_Traurig_LLP_Master_Services_Agreement_Template.docx
2025-07-16 17:19:58,169 - utils.reliable_document_generator - INFO - MSA template exists: True
2025-07-16 17:19:58,169 - utils.reliable_document_generator - INFO - TACI SOW template path: templates/base_templates/taci/SOW_Ransom_Consulting_template.docx
2025-07-16 17:19:58,169 - utils.reliable_document_generator - INFO - Resolved TACI SOW template path: templates/base_templates/taci/SOW_Ransom_Consulting_template.docx
2025-07-16 17:19:58,169 - utils.reliable_document_generator - INFO - TACI SOW template exists: True
2025-07-16 17:19:58,169 - utils.reliable_document_generator - INFO - RR SOW template path: templates/base_templates/rr/SOW_ON_Template_Recovery_&_Restoration_Support.docx
2025-07-16 17:19:58,169 - utils.reliable_document_generator - INFO - Resolved RR SOW template path: templates/base_templates/rr/SOW_ON_Template_Recovery_&_Restoration_Support.docx
2025-07-16 17:19:58,170 - utils.reliable_document_generator - INFO - RR SOW template exists: True
2025-07-16 17:19:58,170 - utils.reliable_document_generator - INFO - All available templates:
2025-07-16 17:19:58,171 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\baa\Template_Business_Associate_Agreement.docx
2025-07-16 17:19:58,171 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\bec\SOW_Baker_GCP_BEC_template.docx
2025-07-16 17:19:58,171 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\bec\SOW_Baker_M365_BEC_template.docx
2025-07-16 17:19:58,171 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\bec\SOW_Chubb_Exchange_BEC_template.docx
2025-07-16 17:19:58,171 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\bec\SOW_Chubb_GCP_BEC_template.docx
2025-07-16 17:19:58,172 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\bec\SOW_Chubb_M365_BEC_template.docx
2025-07-16 17:19:58,172 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\bec\SOW_Coalition_GCP_BEC_template.docx
2025-07-16 17:19:58,172 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\bec\SOW_Coalition_M365_BEC_template.docx
2025-07-16 17:19:58,172 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\bec\SOW_Dykema_GCP_BEC_template.docx
2025-07-16 17:19:58,172 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\bec\SOW_Dykema_M365_BEC_template.docx
2025-07-16 17:19:58,172 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\bec\SOW_Exchange_BEC_template.docx
2025-07-16 17:19:58,172 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\bec\SOW_GCP_BEC_template.docx
2025-07-16 17:19:58,172 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\bec\SOW_M365_BEC_template.docx
2025-07-16 17:19:58,172 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\bec\SOW_Woods_M365_BEC_template.docx
2025-07-16 17:19:58,172 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\dfir\SOW_chubb_IR_cs_codes_template.docx
2025-07-16 17:19:58,172 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\dfir\SOW_Fixed_Fee_IR_template.docx
2025-07-16 17:19:58,172 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\dfir\SOW_IR_BRAP_Beckage_Verbiage_Template.docx
2025-07-16 17:19:58,174 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\dfir\SOW_IR_Template_IR Investigation_Beckage_Template.docx
2025-07-16 17:19:58,174 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\dfir\SOW_IR_Template_single_price_IR_Investigation.docx
2025-07-16 17:19:58,174 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\dfir\SOW_IR_Template_Tokio-Marine_Single Price_IR Investigation.docx
2025-07-16 17:19:58,174 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\dfir\SOW_IR_Template_Woods_Rogers_Only_IR_Investigation.docx
2025-07-16 17:19:58,174 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\dfir\SOW__IR_template_Beazley_FFP.docx
2025-07-16 17:19:58,174 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\dfir\SOW__IR_template_Beazley_FFP_EDR_New_Hybrid.docx
2025-07-16 17:19:58,175 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\dfir\SOW__IR_template_Beazley_FFP_EDR_New_M365.docx
2025-07-16 17:19:58,175 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\dpa\Template_Data_Processing_Agreement.docx
2025-07-16 17:19:58,176 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Baker_&_Hostetler_LLP_(2-Party)_Master_Services_Agreement_Template.docx
2025-07-16 17:19:58,176 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Baker_&_Hostetler_LLP_Beazley_TASB_Master_Services_Agreement_Template.docx
2025-07-16 17:19:58,177 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Baker_Donelson_Bearman_Caldwell_&_Berkowitz_P.C._Master_Services_Agreement_Template.docx
2025-07-16 17:19:58,177 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Cipriani_&_Werner_PC_Master_Services_Agreement_Template.docx
2025-07-16 17:19:58,177 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Clark_Hill_PLC_Master_Services_Agreement.docx
2025-07-16 17:19:58,178 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Constangy_Brooks_Smith_&_Prophete_LLP_Master_Services_Agreement_Template.docx
2025-07-16 17:19:58,178 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Dykema_Gossett_PLLC_Master_Service_Agreement_Template.docx
2025-07-16 17:19:58,178 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Eckert_Seamans_Cherin_&_Mellott_LLC_Master_Service_Agreement_Template.docx
2025-07-16 17:19:58,178 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Gordon_Rees_Scully_Mansukhani_LLP_Master_Services_Agreement_Template.docx
2025-07-16 17:19:58,179 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Greenberg_Traurig_LLP_Master_Services_Agreement_Template.docx
2025-07-16 17:19:58,179 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\IR_Services_2_Party_Master_Services_Agreement_Template.docx
2025-07-16 17:19:58,179 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\IR_Services_3_Party_Master_Services_Agreement_Template.docx
2025-07-16 17:19:58,179 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Jackson_Lewis_PC_Master_Services_Agreement_Template.docx
2025-07-16 17:19:58,179 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Jackson_Lewis_PC_with_BAA_Master_Services_Agreement_Template.docx
2025-07-16 17:19:58,179 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Lewis_Brisbois_Bisgaard_&_Smith_LLP_Master_Services_Agreement_Template.docx
2025-07-16 17:19:58,179 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Locke_Lord_Master_Services_Agreement_Template.docx
2025-07-16 17:19:58,179 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Maynard_Nexsen_PC_Master_Services_Agreement_Template.docx
2025-07-16 17:19:58,179 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\McDonald_Hopkins_LLC_Master_Services_Agreement_Template.docx
2025-07-16 17:19:58,180 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Mullen_Coughlin_LLC_Master_Services_Agreement_Template.docx
2025-07-16 17:19:58,180 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Nelson_Mullins_Riley_&_Scarborough_LLP_Master_Services_Agreement_Template.docx
2025-07-16 17:19:58,180 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Norton_Rose_Fulbright_Master_Services_Agreement_Template.docx
2025-07-16 17:19:58,180 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Octillo_Law_PLLC_Master_Services_Agreement_Template.docx
2025-07-16 17:19:58,180 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Ogletree_Deakins_Master_Services_Agreement_Template.docx
2025-07-16 17:19:58,181 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Ruskin_Moscou_Faltischek_P.C._Master_Services_Agreement_Template.docx
2025-07-16 17:19:58,181 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Shook_Hardy_&_Bacon_LLP_Master_Services_Agreement_Template.docx
2025-07-16 17:19:58,181 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\The_Beckage_Firm_PLLC_Master_Services_Agreement_Template.docx
2025-07-16 17:19:58,181 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Woods_Rogers_Master_Services_Agreement_Template.docx
2025-07-16 17:19:58,181 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\rr\SOW_ON_Template_Beazley_Only_Recovery_&_Restoration_Support.docx
2025-07-16 17:19:58,181 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\rr\SOW_ON_Template_Recovery_&_Restoration_Support.docx
2025-07-16 17:19:58,183 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\taci\SOW_Coalition_Ransom_Consulting_FFP_template.docx
2025-07-16 17:19:58,183 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\taci\SOW_Ransom_Consulting_template.docx
2025-07-16 17:19:58,183 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\taci\SOW_RN-Template_Ransom_Site_Monitoring.docx
2025-07-16 17:19:58,183 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\taci\SOW_RN_Template_Beckage_Firm_Ransom_Consulting.docx
2025-07-16 17:19:58,183 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\taci\SOW_RN_Template_Chubb_Only_Ransom_Consulting.docx
2025-07-16 17:19:58,183 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\taci\SOW_RN_Template_Client_Data_Download.docx
2025-07-16 17:19:58,187 - utils.reliable_document_generator - INFO - Generating documents for DFIR engagement
2025-07-16 17:19:58,189 - utils.reliable_document_generator - INFO - Client: Test100
2025-07-16 17:19:58,189 - utils.reliable_document_generator - INFO - Law Firm: Greenberg Traurig, LLP
2025-07-16 17:19:58,189 - utils.reliable_document_generator - INFO - Output directory: C:/Users/<USER>/Documents/Test_Pace
2025-07-16 17:19:58,189 - utils.reliable_document_generator - INFO - Generating SOW...
2025-07-16 17:19:58,189 - utils.reliable_document_generator - INFO - SOW template path: templates/base_templates/dfir/SOW_IR_Template_single_price_IR_Investigation.docx
2025-07-16 17:19:58,189 - utils.reliable_document_generator - INFO - SOW output path: C:/Users/<USER>/Documents/Test_Pace\Test100 IR Investigation - SOW 20250716.docx
2025-07-16 17:19:58,191 - utils.reliable_document_generator - INFO - Using python-docx to replace placeholders in SOW document
2025-07-16 17:19:58,191 - utils.docx_placeholder_replacer - INFO - Resolved template path: templates/base_templates/dfir/SOW_IR_Template_single_price_IR_Investigation.docx
2025-07-16 17:19:58,191 - utils.docx_placeholder_replacer - INFO - Loading document: templates/base_templates/dfir/SOW_IR_Template_single_price_IR_Investigation.docx
2025-07-16 17:19:58,825 - utils.docx_placeholder_replacer - INFO - Saving document to: C:/Users/<USER>/Documents/Test_Pace\Test100 IR Investigation - SOW 20250716.docx
2025-07-16 17:19:58,837 - utils.docx_placeholder_replacer - INFO - Document generated successfully: C:/Users/<USER>/Documents/Test_Pace\Test100 IR Investigation - SOW 20250716.docx
2025-07-16 17:19:58,837 - utils.reliable_document_generator - INFO - SOW document created successfully with python-docx: C:/Users/<USER>/Documents/Test_Pace\Test100 IR Investigation - SOW 20250716.docx
2025-07-16 17:19:58,837 - utils.reliable_document_generator - INFO - TACI SOW is needed
2025-07-16 17:19:58,838 - utils.reliable_document_generator - INFO - Generating TACI SOW...
2025-07-16 17:19:58,838 - utils.reliable_document_generator - INFO - TACI SOW template path: templates/base_templates/taci/SOW_Ransom_Consulting_template.docx
2025-07-16 17:19:58,838 - utils.reliable_document_generator - INFO - TACI SOW output path: C:/Users/<USER>/Documents/Test_Pace\Test100 Ransom Communications - SOW 20250716.docx
2025-07-16 17:19:58,839 - utils.reliable_document_generator - INFO - Using python-docx to replace placeholders in TACI SOW document
2025-07-16 17:19:58,839 - utils.docx_placeholder_replacer - INFO - Resolved template path: templates/base_templates/taci/SOW_Ransom_Consulting_template.docx
2025-07-16 17:19:58,839 - utils.docx_placeholder_replacer - INFO - Loading document: templates/base_templates/taci/SOW_Ransom_Consulting_template.docx
2025-07-16 17:20:02,414 - utils.docx_placeholder_replacer - INFO - Saving document to: C:/Users/<USER>/Documents/Test_Pace\Test100 Ransom Communications - SOW 20250716.docx
2025-07-16 17:20:02,431 - utils.docx_placeholder_replacer - INFO - Document generated successfully: C:/Users/<USER>/Documents/Test_Pace\Test100 Ransom Communications - SOW 20250716.docx
2025-07-16 17:20:02,431 - utils.reliable_document_generator - INFO - TACI SOW document created successfully with python-docx: C:/Users/<USER>/Documents/Test_Pace\Test100 Ransom Communications - SOW 20250716.docx
2025-07-16 17:20:02,431 - utils.reliable_document_generator - INFO - RR SOW is needed
2025-07-16 17:20:02,431 - utils.reliable_document_generator - INFO - Generating RR SOW...
2025-07-16 17:20:02,432 - utils.reliable_document_generator - INFO - RR SOW template path: templates/base_templates/rr/SOW_ON_Template_Recovery_&_Restoration_Support.docx
2025-07-16 17:20:02,432 - utils.reliable_document_generator - INFO - RR SOW output path: C:/Users/<USER>/Documents/Test_Pace\Test100 Recovery & Remediation - SOW 20250716.docx
2025-07-16 17:20:02,433 - utils.reliable_document_generator - INFO - Using python-docx to replace placeholders in RR SOW document
2025-07-16 17:20:02,433 - utils.docx_placeholder_replacer - INFO - Resolved template path: templates/base_templates/rr/SOW_ON_Template_Recovery_&_Restoration_Support.docx
2025-07-16 17:20:02,433 - utils.docx_placeholder_replacer - INFO - Loading document: templates/base_templates/rr/SOW_ON_Template_Recovery_&_Restoration_Support.docx
2025-07-16 17:20:02,816 - utils.docx_placeholder_replacer - INFO - Saving document to: C:/Users/<USER>/Documents/Test_Pace\Test100 Recovery & Remediation - SOW 20250716.docx
2025-07-16 17:20:02,831 - utils.docx_placeholder_replacer - INFO - Document generated successfully: C:/Users/<USER>/Documents/Test_Pace\Test100 Recovery & Remediation - SOW 20250716.docx
2025-07-16 17:20:02,831 - utils.reliable_document_generator - INFO - RR SOW document created successfully with python-docx: C:/Users/<USER>/Documents/Test_Pace\Test100 Recovery & Remediation - SOW 20250716.docx
2025-07-16 17:20:02,832 - utils.reliable_document_generator - INFO - Generating MSA...
2025-07-16 17:20:02,832 - utils.reliable_document_generator - INFO - MSA template path: templates/base_templates/msa_templates/Greenberg_Traurig_LLP_Master_Services_Agreement_Template.docx
2025-07-16 17:20:02,832 - utils.reliable_document_generator - INFO - MSA output path: C:/Users/<USER>/Documents/Test_Pace\Test100 Master Services Agreement (MSA) 20250716.docx
2025-07-16 17:20:02,833 - utils.reliable_document_generator - INFO - Using python-docx to replace placeholders in MSA document
2025-07-16 17:20:02,833 - utils.docx_placeholder_replacer - INFO - Resolved template path: templates/base_templates/msa_templates/Greenberg_Traurig_LLP_Master_Services_Agreement_Template.docx
2025-07-16 17:20:02,834 - utils.docx_placeholder_replacer - INFO - Loading document: templates/base_templates/msa_templates/Greenberg_Traurig_LLP_Master_Services_Agreement_Template.docx
2025-07-16 17:20:03,290 - utils.docx_placeholder_replacer - INFO - Saving document to: C:/Users/<USER>/Documents/Test_Pace\Test100 Master Services Agreement (MSA) 20250716.docx
2025-07-16 17:20:03,299 - utils.docx_placeholder_replacer - INFO - Document generated successfully: C:/Users/<USER>/Documents/Test_Pace\Test100 Master Services Agreement (MSA) 20250716.docx
2025-07-16 17:20:03,299 - utils.reliable_document_generator - INFO - MSA document created successfully with python-docx: C:/Users/<USER>/Documents/Test_Pace\Test100 Master Services Agreement (MSA) 20250716.docx
2025-07-16 17:20:03,299 - utils.reliable_document_generator - INFO - Generated 4 documents:
2025-07-16 17:20:03,299 - utils.reliable_document_generator - INFO - Document 1: C:/Users/<USER>/Documents/Test_Pace\Test100 IR Investigation - SOW 20250716.docx
2025-07-16 17:20:03,300 - utils.reliable_document_generator - INFO - Document 2: C:/Users/<USER>/Documents/Test_Pace\Test100 Ransom Communications - SOW 20250716.docx
2025-07-16 17:20:03,300 - utils.reliable_document_generator - INFO - Document 3: C:/Users/<USER>/Documents/Test_Pace\Test100 Recovery & Remediation - SOW 20250716.docx
2025-07-16 17:20:03,300 - utils.reliable_document_generator - INFO - Document 4: C:/Users/<USER>/Documents/Test_Pace\Test100 Master Services Agreement (MSA) 20250716.docx
2025-07-16 17:31:29,109 - root - INFO - Starting PACE application
2025-07-16 17:31:29,109 - root - INFO - Python version: 3.12.7 (tags/v3.12.7:0b05ead, Oct  1 2024, 03:06:41) [MSC v.1941 64 bit (AMD64)]
2025-07-16 17:31:29,110 - root - INFO - Current directory: D:\PACE_1.1.3
2025-07-16 17:31:29,110 - root - INFO - Script directory: D:\PACE_1.1.3
2025-07-16 17:31:29,110 - root - INFO - Executable directory: D:\PACE_1.1.3\venv\Scripts
2025-07-16 17:31:29,152 - root - INFO - Checking for icon at: PACE.ico (exists: True)
2025-07-16 17:31:29,153 - root - INFO - Checking for icon at: D:\PACE_1.1.3\PACE.ico (exists: True)
2025-07-16 17:31:29,153 - root - INFO - Checking for icon at: D:\PACE_1.1.3\venv\Scripts\PACE.ico (exists: False)
2025-07-16 17:31:29,153 - root - INFO - Checking for icon at: app_icon.ico (exists: False)
2025-07-16 17:31:29,153 - root - INFO - Checking for icon at: D:\PACE_1.1.3\app_icon.ico (exists: False)
2025-07-16 17:31:29,153 - root - INFO - Checking for icon at: D:\PACE_1.1.3\venv\Scripts\app_icon.ico (exists: False)
2025-07-16 17:31:29,240 - root - INFO - Found and loaded icon from: PACE.ico
2025-07-16 17:31:29,870 - root - INFO - Main window created and shown successfully
2025-07-16 17:32:17,373 - utils.reliable_document_generator - INFO - Using output directory: C:/Users/<USER>/Documents/Test_Pace
2025-07-16 17:32:17,374 - utils.reliable_document_generator - INFO - ReliableDocumentGenerator initialized with output directory: C:/Users/<USER>/Documents/Test_Pace
2025-07-16 17:32:17,375 - utils.reliable_document_generator - INFO - Debugging template paths...
2025-07-16 17:32:17,375 - utils.reliable_document_generator - INFO - SOW template path: templates/base_templates/dfir/SOW_IR_Template_single_price_IR_Investigation.docx
2025-07-16 17:32:17,376 - utils.reliable_document_generator - INFO - Resolved SOW template path: templates/base_templates/dfir/SOW_IR_Template_single_price_IR_Investigation.docx
2025-07-16 17:32:17,376 - utils.reliable_document_generator - INFO - SOW template exists: True
2025-07-16 17:32:17,377 - utils.reliable_document_generator - INFO - MSA template path: templates/base_templates/msa_templates/Greenberg_Traurig_LLP_Master_Services_Agreement_Template.docx
2025-07-16 17:32:17,377 - utils.reliable_document_generator - INFO - Resolved MSA template path: templates/base_templates/msa_templates/Greenberg_Traurig_LLP_Master_Services_Agreement_Template.docx
2025-07-16 17:32:17,377 - utils.reliable_document_generator - INFO - MSA template exists: True
2025-07-16 17:32:17,378 - utils.reliable_document_generator - INFO - TACI SOW template path: templates/base_templates/taci/SOW_Ransom_Consulting_template.docx
2025-07-16 17:32:17,378 - utils.reliable_document_generator - INFO - Resolved TACI SOW template path: templates/base_templates/taci/SOW_Ransom_Consulting_template.docx
2025-07-16 17:32:17,378 - utils.reliable_document_generator - INFO - TACI SOW template exists: True
2025-07-16 17:32:17,379 - utils.reliable_document_generator - INFO - RR SOW template path: templates/base_templates/rr/SOW_ON_Template_Recovery_&_Restoration_Support.docx
2025-07-16 17:32:17,379 - utils.reliable_document_generator - INFO - Resolved RR SOW template path: templates/base_templates/rr/SOW_ON_Template_Recovery_&_Restoration_Support.docx
2025-07-16 17:32:17,379 - utils.reliable_document_generator - INFO - RR SOW template exists: True
2025-07-16 17:32:17,379 - utils.reliable_document_generator - INFO - All available templates:
2025-07-16 17:32:17,380 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\baa\Template_Business_Associate_Agreement.docx
2025-07-16 17:32:17,381 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\bec\SOW_Baker_GCP_BEC_template.docx
2025-07-16 17:32:17,381 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\bec\SOW_Baker_M365_BEC_template.docx
2025-07-16 17:32:17,381 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\bec\SOW_Chubb_Exchange_BEC_template.docx
2025-07-16 17:32:17,381 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\bec\SOW_Chubb_GCP_BEC_template.docx
2025-07-16 17:32:17,381 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\bec\SOW_Chubb_M365_BEC_template.docx
2025-07-16 17:32:17,381 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\bec\SOW_Coalition_GCP_BEC_template.docx
2025-07-16 17:32:17,381 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\bec\SOW_Coalition_M365_BEC_template.docx
2025-07-16 17:32:17,381 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\bec\SOW_Dykema_GCP_BEC_template.docx
2025-07-16 17:32:17,382 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\bec\SOW_Dykema_M365_BEC_template.docx
2025-07-16 17:32:17,382 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\bec\SOW_Exchange_BEC_template.docx
2025-07-16 17:32:17,382 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\bec\SOW_GCP_BEC_template.docx
2025-07-16 17:32:17,382 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\bec\SOW_M365_BEC_template.docx
2025-07-16 17:32:17,383 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\bec\SOW_Woods_M365_BEC_template.docx
2025-07-16 17:32:17,383 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\dfir\SOW_chubb_IR_cs_codes_template.docx
2025-07-16 17:32:17,383 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\dfir\SOW_Fixed_Fee_IR_template.docx
2025-07-16 17:32:17,383 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\dfir\SOW_IR_BRAP_Beckage_Verbiage_Template.docx
2025-07-16 17:32:17,383 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\dfir\SOW_IR_Template_IR Investigation_Beckage_Template.docx
2025-07-16 17:32:17,383 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\dfir\SOW_IR_Template_single_price_IR_Investigation.docx
2025-07-16 17:32:17,384 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\dfir\SOW_IR_Template_Tokio-Marine_Single Price_IR Investigation.docx
2025-07-16 17:32:17,384 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\dfir\SOW_IR_Template_Woods_Rogers_Only_IR_Investigation.docx
2025-07-16 17:32:17,384 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\dfir\SOW__IR_template_Beazley_FFP.docx
2025-07-16 17:32:17,384 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\dfir\SOW__IR_template_Beazley_FFP_EDR_New_Hybrid.docx
2025-07-16 17:32:17,384 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\dfir\SOW__IR_template_Beazley_FFP_EDR_New_M365.docx
2025-07-16 17:32:17,384 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\dpa\Template_Data_Processing_Agreement.docx
2025-07-16 17:32:17,384 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Baker_&_Hostetler_LLP_(2-Party)_Master_Services_Agreement_Template.docx
2025-07-16 17:32:17,384 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Baker_&_Hostetler_LLP_Beazley_TASB_Master_Services_Agreement_Template.docx
2025-07-16 17:32:17,385 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Baker_Donelson_Bearman_Caldwell_&_Berkowitz_P.C._Master_Services_Agreement_Template.docx
2025-07-16 17:32:17,385 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Cipriani_&_Werner_PC_Master_Services_Agreement_Template.docx
2025-07-16 17:32:17,385 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Clark_Hill_PLC_Master_Services_Agreement.docx
2025-07-16 17:32:17,385 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Constangy_Brooks_Smith_&_Prophete_LLP_Master_Services_Agreement_Template.docx
2025-07-16 17:32:17,385 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Dykema_Gossett_PLLC_Master_Service_Agreement_Template.docx
2025-07-16 17:32:17,385 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Eckert_Seamans_Cherin_&_Mellott_LLC_Master_Service_Agreement_Template.docx
2025-07-16 17:32:17,385 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Gordon_Rees_Scully_Mansukhani_LLP_Master_Services_Agreement_Template.docx
2025-07-16 17:32:17,385 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Greenberg_Traurig_LLP_Master_Services_Agreement_Template.docx
2025-07-16 17:32:17,385 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\IR_Services_2_Party_Master_Services_Agreement_Template.docx
2025-07-16 17:32:17,386 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\IR_Services_3_Party_Master_Services_Agreement_Template.docx
2025-07-16 17:32:17,386 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Jackson_Lewis_PC_Master_Services_Agreement_Template.docx
2025-07-16 17:32:17,386 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Jackson_Lewis_PC_with_BAA_Master_Services_Agreement_Template.docx
2025-07-16 17:32:17,386 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Lewis_Brisbois_Bisgaard_&_Smith_LLP_Master_Services_Agreement_Template.docx
2025-07-16 17:32:17,386 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Locke_Lord_Master_Services_Agreement_Template.docx
2025-07-16 17:32:17,386 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Maynard_Nexsen_PC_Master_Services_Agreement_Template.docx
2025-07-16 17:32:17,387 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\McDonald_Hopkins_LLC_Master_Services_Agreement_Template.docx
2025-07-16 17:32:17,387 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Mullen_Coughlin_LLC_Master_Services_Agreement_Template.docx
2025-07-16 17:32:17,387 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Nelson_Mullins_Riley_&_Scarborough_LLP_Master_Services_Agreement_Template.docx
2025-07-16 17:32:17,387 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Norton_Rose_Fulbright_Master_Services_Agreement_Template.docx
2025-07-16 17:32:17,387 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Octillo_Law_PLLC_Master_Services_Agreement_Template.docx
2025-07-16 17:32:17,387 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Ogletree_Deakins_Master_Services_Agreement_Template.docx
2025-07-16 17:32:17,387 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Ruskin_Moscou_Faltischek_P.C._Master_Services_Agreement_Template.docx
2025-07-16 17:32:17,387 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Shook_Hardy_&_Bacon_LLP_Master_Services_Agreement_Template.docx
2025-07-16 17:32:17,387 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\The_Beckage_Firm_PLLC_Master_Services_Agreement_Template.docx
2025-07-16 17:32:17,387 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Woods_Rogers_Master_Services_Agreement_Template.docx
2025-07-16 17:32:17,388 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\rr\SOW_ON_Template_Beazley_Only_Recovery_&_Restoration_Support.docx
2025-07-16 17:32:17,388 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\rr\SOW_ON_Template_Recovery_&_Restoration_Support.docx
2025-07-16 17:32:17,388 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\taci\SOW_Coalition_Ransom_Consulting_FFP_template.docx
2025-07-16 17:32:17,388 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\taci\SOW_Ransom_Consulting_template.docx
2025-07-16 17:32:17,388 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\taci\SOW_RN-Template_Ransom_Site_Monitoring.docx
2025-07-16 17:32:17,388 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\taci\SOW_RN_Template_Beckage_Firm_Ransom_Consulting.docx
2025-07-16 17:32:17,388 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\taci\SOW_RN_Template_Chubb_Only_Ransom_Consulting.docx
2025-07-16 17:32:17,388 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\taci\SOW_RN_Template_Client_Data_Download.docx
2025-07-16 17:32:17,394 - utils.reliable_document_generator - INFO - Generating documents for DFIR engagement
2025-07-16 17:32:17,394 - utils.reliable_document_generator - INFO - Client: Test100
2025-07-16 17:32:17,394 - utils.reliable_document_generator - INFO - Law Firm: Greenberg Traurig, LLP
2025-07-16 17:32:17,395 - utils.reliable_document_generator - INFO - Output directory: C:/Users/<USER>/Documents/Test_Pace
2025-07-16 17:32:17,395 - utils.reliable_document_generator - INFO - Generating SOW...
2025-07-16 17:32:17,395 - utils.reliable_document_generator - INFO - SOW template path: templates/base_templates/dfir/SOW_IR_Template_single_price_IR_Investigation.docx
2025-07-16 17:32:17,395 - utils.reliable_document_generator - INFO - SOW output path: C:/Users/<USER>/Documents/Test_Pace\Test100 IR Investigation - SOW 20250716.docx
2025-07-16 17:32:17,396 - utils.reliable_document_generator - INFO - Using python-docx to replace placeholders in SOW document
2025-07-16 17:32:17,396 - utils.docx_placeholder_replacer - INFO - Resolved template path: templates/base_templates/dfir/SOW_IR_Template_single_price_IR_Investigation.docx
2025-07-16 17:32:17,396 - utils.docx_placeholder_replacer - INFO - Loading document: templates/base_templates/dfir/SOW_IR_Template_single_price_IR_Investigation.docx
2025-07-16 17:32:18,014 - utils.docx_placeholder_replacer - INFO - Saving document to: C:/Users/<USER>/Documents/Test_Pace\Test100 IR Investigation - SOW 20250716.docx
2025-07-16 17:32:18,016 - utils.docx_placeholder_replacer - WARNING - Permission denied when saving to C:/Users/<USER>/Documents/Test_Pace\Test100 IR Investigation - SOW 20250716.docx, trying with a temporary file
2025-07-16 17:32:18,032 - utils.docx_placeholder_replacer - ERROR - Error removing original file: [WinError 32] The process cannot access the file because it is being used by another process: 'C:/Users/<USER>/Documents/Test_Pace\\Test100 IR Investigation - SOW 20250716.docx'
2025-07-16 17:32:18,032 - utils.reliable_document_generator - INFO - Falling back to generate_document_reliable for SOW document
2025-07-16 17:32:18,032 - utils.reliable_document_generator - INFO - Generating document from templates/base_templates/dfir/SOW_IR_Template_single_price_IR_Investigation.docx to C:/Users/<USER>/Documents/Test_Pace\Test100 IR Investigation - SOW 20250716.docx
2025-07-16 17:32:18,034 - utils.reliable_document_generator - INFO - Resolved template path: templates/base_templates/dfir/SOW_IR_Template_single_price_IR_Investigation.docx
2025-07-16 17:32:18,034 - utils.reliable_document_generator - INFO - Loading template: templates/base_templates/dfir/SOW_IR_Template_single_price_IR_Investigation.docx
2025-07-16 17:32:18,034 - utils.reliable_document_generator - INFO - Creating custom Jinja2 environment
2025-07-16 17:32:18,034 - utils.reliable_document_generator - INFO - Preserving RichText object for client
2025-07-16 17:32:18,034 - utils.reliable_document_generator - INFO - Preserving RichText object for Client
2025-07-16 17:32:18,034 - utils.reliable_document_generator - INFO - Preserving RichText object for client_name
2025-07-16 17:32:18,034 - utils.reliable_document_generator - INFO - Preserving RichText object for company
2025-07-16 17:32:18,035 - utils.reliable_document_generator - INFO - Preserving RichText object for client_address
2025-07-16 17:32:18,035 - utils.reliable_document_generator - INFO - Preserving RichText object for address
2025-07-16 17:32:18,035 - utils.reliable_document_generator - INFO - Preserving RichText object for law_firm
2025-07-16 17:32:18,035 - utils.reliable_document_generator - INFO - Preserving RichText object for Lawfirm
2025-07-16 17:32:18,035 - utils.reliable_document_generator - INFO - Preserving RichText object for lawfirm
2025-07-16 17:32:18,035 - utils.reliable_document_generator - INFO - Preserving RichText object for firm
2025-07-16 17:32:18,035 - utils.reliable_document_generator - INFO - Preserving RichText object for counsel
2025-07-16 17:32:18,035 - utils.reliable_document_generator - INFO - Preserving RichText object for CLIENT
2025-07-16 17:32:18,035 - utils.reliable_document_generator - INFO - Preserving RichText object for CLIENT_NAME
2025-07-16 17:32:18,036 - utils.reliable_document_generator - INFO - Rendering template with placeholders
2025-07-16 17:32:18,137 - utils.reliable_document_generator - INFO - Saving document to: C:/Users/<USER>/Documents/Test_Pace\Test100 IR Investigation - SOW 20250716.docx
2025-07-16 17:32:18,148 - utils.reliable_document_generator - ERROR - Error saving document: [WinError 32] The process cannot access the file because it is being used by another process: 'C:/Users/<USER>/Documents/Test_Pace\\Test100 IR Investigation - SOW 20250716.docx'
2025-07-16 17:32:18,149 - utils.reliable_document_generator - ERROR - Traceback (most recent call last):
  File "D:\PACE_1.1.3\utils\reliable_document_generator.py", line 174, in generate_document_reliable
    os.remove(output_path)  # Remove existing file if it exists
    ^^^^^^^^^^^^^^^^^^^^^^
PermissionError: [WinError 32] The process cannot access the file because it is being used by another process: 'C:/Users/<USER>/Documents/Test_Pace\\Test100 IR Investigation - SOW 20250716.docx'

2025-07-16 17:32:18,149 - utils.reliable_document_generator - INFO - Attempting direct copy fallback due to save error
2025-07-16 17:32:18,152 - utils.reliable_document_generator - ERROR - Error creating fallback copy: [WinError 32] The process cannot access the file because it is being used by another process
2025-07-16 17:32:18,152 - utils.reliable_document_generator - INFO - TACI SOW is needed
2025-07-16 17:32:18,152 - utils.reliable_document_generator - INFO - Generating TACI SOW...
2025-07-16 17:32:18,152 - utils.reliable_document_generator - INFO - TACI SOW template path: templates/base_templates/taci/SOW_Ransom_Consulting_template.docx
2025-07-16 17:32:18,153 - utils.reliable_document_generator - INFO - TACI SOW output path: C:/Users/<USER>/Documents/Test_Pace\Test100 Ransom Communications - SOW 20250716.docx
2025-07-16 17:32:18,154 - utils.reliable_document_generator - INFO - Using python-docx to replace placeholders in TACI SOW document
2025-07-16 17:32:18,154 - utils.docx_placeholder_replacer - INFO - Resolved template path: templates/base_templates/taci/SOW_Ransom_Consulting_template.docx
2025-07-16 17:32:18,154 - utils.docx_placeholder_replacer - INFO - Loading document: templates/base_templates/taci/SOW_Ransom_Consulting_template.docx
2025-07-16 17:32:18,579 - utils.docx_placeholder_replacer - INFO - Saving document to: C:/Users/<USER>/Documents/Test_Pace\Test100 Ransom Communications - SOW 20250716.docx
2025-07-16 17:32:18,587 - utils.docx_placeholder_replacer - INFO - Document generated successfully: C:/Users/<USER>/Documents/Test_Pace\Test100 Ransom Communications - SOW 20250716.docx
2025-07-16 17:32:18,587 - utils.reliable_document_generator - INFO - TACI SOW document created successfully with python-docx: C:/Users/<USER>/Documents/Test_Pace\Test100 Ransom Communications - SOW 20250716.docx
2025-07-16 17:32:18,587 - utils.reliable_document_generator - INFO - RR SOW is needed
2025-07-16 17:32:18,587 - utils.reliable_document_generator - INFO - Generating RR SOW...
2025-07-16 17:32:18,587 - utils.reliable_document_generator - INFO - RR SOW template path: templates/base_templates/rr/SOW_ON_Template_Recovery_&_Restoration_Support.docx
2025-07-16 17:32:18,588 - utils.reliable_document_generator - INFO - RR SOW output path: C:/Users/<USER>/Documents/Test_Pace\Test100 Recovery & Remediation - SOW 20250716.docx
2025-07-16 17:32:18,588 - utils.reliable_document_generator - INFO - Using python-docx to replace placeholders in RR SOW document
2025-07-16 17:32:18,588 - utils.docx_placeholder_replacer - INFO - Resolved template path: templates/base_templates/rr/SOW_ON_Template_Recovery_&_Restoration_Support.docx
2025-07-16 17:32:18,588 - utils.docx_placeholder_replacer - INFO - Loading document: templates/base_templates/rr/SOW_ON_Template_Recovery_&_Restoration_Support.docx
2025-07-16 17:32:19,029 - utils.docx_placeholder_replacer - INFO - Saving document to: C:/Users/<USER>/Documents/Test_Pace\Test100 Recovery & Remediation - SOW 20250716.docx
2025-07-16 17:32:19,036 - utils.docx_placeholder_replacer - INFO - Document generated successfully: C:/Users/<USER>/Documents/Test_Pace\Test100 Recovery & Remediation - SOW 20250716.docx
2025-07-16 17:32:19,036 - utils.reliable_document_generator - INFO - RR SOW document created successfully with python-docx: C:/Users/<USER>/Documents/Test_Pace\Test100 Recovery & Remediation - SOW 20250716.docx
2025-07-16 17:32:19,036 - utils.reliable_document_generator - INFO - Generating MSA...
2025-07-16 17:32:19,036 - utils.reliable_document_generator - INFO - MSA template path: templates/base_templates/msa_templates/Greenberg_Traurig_LLP_Master_Services_Agreement_Template.docx
2025-07-16 17:32:19,037 - utils.reliable_document_generator - INFO - MSA output path: C:/Users/<USER>/Documents/Test_Pace\Test100 Master Services Agreement (MSA) 20250716.docx
2025-07-16 17:32:19,037 - utils.reliable_document_generator - INFO - Using python-docx to replace placeholders in MSA document
2025-07-16 17:32:19,037 - utils.docx_placeholder_replacer - INFO - Resolved template path: templates/base_templates/msa_templates/Greenberg_Traurig_LLP_Master_Services_Agreement_Template.docx
2025-07-16 17:32:19,041 - utils.docx_placeholder_replacer - INFO - Loading document: templates/base_templates/msa_templates/Greenberg_Traurig_LLP_Master_Services_Agreement_Template.docx
2025-07-16 17:32:19,517 - utils.docx_placeholder_replacer - INFO - Saving document to: C:/Users/<USER>/Documents/Test_Pace\Test100 Master Services Agreement (MSA) 20250716.docx
2025-07-16 17:32:19,529 - utils.docx_placeholder_replacer - INFO - Document generated successfully: C:/Users/<USER>/Documents/Test_Pace\Test100 Master Services Agreement (MSA) 20250716.docx
2025-07-16 17:32:19,529 - utils.reliable_document_generator - INFO - MSA document created successfully with python-docx: C:/Users/<USER>/Documents/Test_Pace\Test100 Master Services Agreement (MSA) 20250716.docx
2025-07-16 17:32:19,529 - utils.reliable_document_generator - INFO - Generated 3 documents:
2025-07-16 17:32:19,530 - utils.reliable_document_generator - INFO - Document 1: C:/Users/<USER>/Documents/Test_Pace\Test100 Ransom Communications - SOW 20250716.docx
2025-07-16 17:32:19,530 - utils.reliable_document_generator - INFO - Document 2: C:/Users/<USER>/Documents/Test_Pace\Test100 Recovery & Remediation - SOW 20250716.docx
2025-07-16 17:32:19,530 - utils.reliable_document_generator - INFO - Document 3: C:/Users/<USER>/Documents/Test_Pace\Test100 Master Services Agreement (MSA) 20250716.docx
2025-07-16 17:44:32,633 - root - INFO - Starting PACE application
2025-07-16 17:44:32,633 - root - INFO - Python version: 3.12.7 (tags/v3.12.7:0b05ead, Oct  1 2024, 03:06:41) [MSC v.1941 64 bit (AMD64)]
2025-07-16 17:44:32,634 - root - INFO - Current directory: D:\PACE_1.1.3
2025-07-16 17:44:32,634 - root - INFO - Script directory: D:\PACE_1.1.3
2025-07-16 17:44:32,634 - root - INFO - Executable directory: D:\PACE_1.1.3\venv\Scripts
2025-07-16 17:44:32,686 - root - INFO - Checking for icon at: PACE.ico (exists: True)
2025-07-16 17:44:32,686 - root - INFO - Checking for icon at: D:\PACE_1.1.3\PACE.ico (exists: True)
2025-07-16 17:44:32,686 - root - INFO - Checking for icon at: D:\PACE_1.1.3\venv\Scripts\PACE.ico (exists: False)
2025-07-16 17:44:32,687 - root - INFO - Checking for icon at: app_icon.ico (exists: False)
2025-07-16 17:44:32,687 - root - INFO - Checking for icon at: D:\PACE_1.1.3\app_icon.ico (exists: False)
2025-07-16 17:44:32,687 - root - INFO - Checking for icon at: D:\PACE_1.1.3\venv\Scripts\app_icon.ico (exists: False)
2025-07-16 17:44:32,847 - root - INFO - Found and loaded icon from: PACE.ico
2025-07-16 17:44:33,713 - root - INFO - Main window created and shown successfully
2025-07-16 17:45:31,021 - utils.reliable_document_generator - INFO - Using output directory: C:/Users/<USER>/Documents/Test_Pace
2025-07-16 17:45:31,021 - utils.reliable_document_generator - INFO - ReliableDocumentGenerator initialized with output directory: C:/Users/<USER>/Documents/Test_Pace
2025-07-16 17:45:31,021 - utils.reliable_document_generator - INFO - Debugging template paths...
2025-07-16 17:45:31,022 - utils.reliable_document_generator - INFO - SOW template path: templates/base_templates/dfir/SOW_IR_Template_single_price_IR_Investigation.docx
2025-07-16 17:45:31,022 - utils.reliable_document_generator - INFO - Resolved SOW template path: templates/base_templates/dfir/SOW_IR_Template_single_price_IR_Investigation.docx
2025-07-16 17:45:31,022 - utils.reliable_document_generator - INFO - SOW template exists: True
2025-07-16 17:45:31,022 - utils.reliable_document_generator - INFO - MSA template path: templates/base_templates/msa_templates/Greenberg_Traurig_LLP_Master_Services_Agreement_Template.docx
2025-07-16 17:45:31,022 - utils.reliable_document_generator - INFO - Resolved MSA template path: templates/base_templates/msa_templates/Greenberg_Traurig_LLP_Master_Services_Agreement_Template.docx
2025-07-16 17:45:31,024 - utils.reliable_document_generator - INFO - MSA template exists: True
2025-07-16 17:45:31,024 - utils.reliable_document_generator - INFO - TACI SOW template path: templates/base_templates/taci/SOW_Ransom_Consulting_template.docx
2025-07-16 17:45:31,024 - utils.reliable_document_generator - INFO - Resolved TACI SOW template path: templates/base_templates/taci/SOW_Ransom_Consulting_template.docx
2025-07-16 17:45:31,025 - utils.reliable_document_generator - INFO - TACI SOW template exists: True
2025-07-16 17:45:31,025 - utils.reliable_document_generator - INFO - RR SOW template path: templates/base_templates/rr/SOW_ON_Template_Recovery_&_Restoration_Support.docx
2025-07-16 17:45:31,025 - utils.reliable_document_generator - INFO - Resolved RR SOW template path: templates/base_templates/rr/SOW_ON_Template_Recovery_&_Restoration_Support.docx
2025-07-16 17:45:31,025 - utils.reliable_document_generator - INFO - RR SOW template exists: True
2025-07-16 17:45:31,025 - utils.reliable_document_generator - INFO - All available templates:
2025-07-16 17:45:31,026 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\baa\Template_Business_Associate_Agreement.docx
2025-07-16 17:45:31,026 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\bec\SOW_Baker_GCP_BEC_template.docx
2025-07-16 17:45:31,026 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\bec\SOW_Baker_M365_BEC_template.docx
2025-07-16 17:45:31,027 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\bec\SOW_Chubb_Exchange_BEC_template.docx
2025-07-16 17:45:31,027 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\bec\SOW_Chubb_GCP_BEC_template.docx
2025-07-16 17:45:31,027 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\bec\SOW_Chubb_M365_BEC_template.docx
2025-07-16 17:45:31,027 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\bec\SOW_Coalition_GCP_BEC_template.docx
2025-07-16 17:45:31,027 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\bec\SOW_Coalition_M365_BEC_template.docx
2025-07-16 17:45:31,027 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\bec\SOW_Dykema_GCP_BEC_template.docx
2025-07-16 17:45:31,027 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\bec\SOW_Dykema_M365_BEC_template.docx
2025-07-16 17:45:31,027 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\bec\SOW_Exchange_BEC_template.docx
2025-07-16 17:45:31,028 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\bec\SOW_GCP_BEC_template.docx
2025-07-16 17:45:31,028 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\bec\SOW_M365_BEC_template.docx
2025-07-16 17:45:31,028 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\bec\SOW_Woods_M365_BEC_template.docx
2025-07-16 17:45:31,028 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\dfir\SOW_chubb_IR_cs_codes_template.docx
2025-07-16 17:45:31,028 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\dfir\SOW_Fixed_Fee_IR_template.docx
2025-07-16 17:45:31,028 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\dfir\SOW_IR_BRAP_Beckage_Verbiage_Template.docx
2025-07-16 17:45:31,028 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\dfir\SOW_IR_Template_IR Investigation_Beckage_Template.docx
2025-07-16 17:45:31,028 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\dfir\SOW_IR_Template_single_price_IR_Investigation.docx
2025-07-16 17:45:31,028 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\dfir\SOW_IR_Template_Tokio-Marine_Single Price_IR Investigation.docx
2025-07-16 17:45:31,029 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\dfir\SOW_IR_Template_Woods_Rogers_Only_IR_Investigation.docx
2025-07-16 17:45:31,029 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\dfir\SOW__IR_template_Beazley_FFP.docx
2025-07-16 17:45:31,029 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\dfir\SOW__IR_template_Beazley_FFP_EDR_New_Hybrid.docx
2025-07-16 17:45:31,029 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\dfir\SOW__IR_template_Beazley_FFP_EDR_New_M365.docx
2025-07-16 17:45:31,029 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\dpa\Template_Data_Processing_Agreement.docx
2025-07-16 17:45:31,029 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Baker_&_Hostetler_LLP_(2-Party)_Master_Services_Agreement_Template.docx
2025-07-16 17:45:31,029 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Baker_&_Hostetler_LLP_Beazley_TASB_Master_Services_Agreement_Template.docx
2025-07-16 17:45:31,029 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Baker_Donelson_Bearman_Caldwell_&_Berkowitz_P.C._Master_Services_Agreement_Template.docx
2025-07-16 17:45:31,029 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Cipriani_&_Werner_PC_Master_Services_Agreement_Template.docx
2025-07-16 17:45:31,030 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Clark_Hill_PLC_Master_Services_Agreement.docx
2025-07-16 17:45:31,030 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Constangy_Brooks_Smith_&_Prophete_LLP_Master_Services_Agreement_Template.docx
2025-07-16 17:45:31,030 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Dykema_Gossett_PLLC_Master_Service_Agreement_Template.docx
2025-07-16 17:45:31,030 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Eckert_Seamans_Cherin_&_Mellott_LLC_Master_Service_Agreement_Template.docx
2025-07-16 17:45:31,030 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Gordon_Rees_Scully_Mansukhani_LLP_Master_Services_Agreement_Template.docx
2025-07-16 17:45:31,030 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Greenberg_Traurig_LLP_Master_Services_Agreement_Template.docx
2025-07-16 17:45:31,030 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\IR_Services_2_Party_Master_Services_Agreement_Template.docx
2025-07-16 17:45:31,030 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\IR_Services_3_Party_Master_Services_Agreement_Template.docx
2025-07-16 17:45:31,030 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Jackson_Lewis_PC_Master_Services_Agreement_Template.docx
2025-07-16 17:45:31,030 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Jackson_Lewis_PC_with_BAA_Master_Services_Agreement_Template.docx
2025-07-16 17:45:31,031 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Lewis_Brisbois_Bisgaard_&_Smith_LLP_Master_Services_Agreement_Template.docx
2025-07-16 17:45:31,031 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Locke_Lord_Master_Services_Agreement_Template.docx
2025-07-16 17:45:31,031 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Maynard_Nexsen_PC_Master_Services_Agreement_Template.docx
2025-07-16 17:45:31,032 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\McDonald_Hopkins_LLC_Master_Services_Agreement_Template.docx
2025-07-16 17:45:31,032 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Mullen_Coughlin_LLC_Master_Services_Agreement_Template.docx
2025-07-16 17:45:31,032 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Nelson_Mullins_Riley_&_Scarborough_LLP_Master_Services_Agreement_Template.docx
2025-07-16 17:45:31,032 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Norton_Rose_Fulbright_Master_Services_Agreement_Template.docx
2025-07-16 17:45:31,032 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Octillo_Law_PLLC_Master_Services_Agreement_Template.docx
2025-07-16 17:45:31,032 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Ogletree_Deakins_Master_Services_Agreement_Template.docx
2025-07-16 17:45:31,032 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Ruskin_Moscou_Faltischek_P.C._Master_Services_Agreement_Template.docx
2025-07-16 17:45:31,033 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Shook_Hardy_&_Bacon_LLP_Master_Services_Agreement_Template.docx
2025-07-16 17:45:31,033 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\The_Beckage_Firm_PLLC_Master_Services_Agreement_Template.docx
2025-07-16 17:45:31,033 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Woods_Rogers_Master_Services_Agreement_Template.docx
2025-07-16 17:45:31,033 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\rr\SOW_ON_Template_Beazley_Only_Recovery_&_Restoration_Support.docx
2025-07-16 17:45:31,033 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\rr\SOW_ON_Template_Recovery_&_Restoration_Support.docx
2025-07-16 17:45:31,033 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\taci\SOW_Coalition_Ransom_Consulting_FFP_template.docx
2025-07-16 17:45:31,033 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\taci\SOW_Ransom_Consulting_template.docx
2025-07-16 17:45:31,033 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\taci\SOW_RN-Template_Ransom_Site_Monitoring.docx
2025-07-16 17:45:31,033 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\taci\SOW_RN_Template_Beckage_Firm_Ransom_Consulting.docx
2025-07-16 17:45:31,034 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\taci\SOW_RN_Template_Chubb_Only_Ransom_Consulting.docx
2025-07-16 17:45:31,034 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\taci\SOW_RN_Template_Client_Data_Download.docx
2025-07-16 17:45:31,037 - utils.reliable_document_generator - INFO - Generating documents for DFIR engagement
2025-07-16 17:45:31,037 - utils.reliable_document_generator - INFO - Client: Bobs name here 
2025-07-16 17:45:31,037 - utils.reliable_document_generator - INFO - Law Firm: Greenberg Traurig, LLP
2025-07-16 17:45:31,037 - utils.reliable_document_generator - INFO - Output directory: C:/Users/<USER>/Documents/Test_Pace
2025-07-16 17:45:31,038 - utils.reliable_document_generator - INFO - Generating SOW...
2025-07-16 17:45:31,039 - utils.reliable_document_generator - INFO - SOW template path: templates/base_templates/dfir/SOW_IR_Template_single_price_IR_Investigation.docx
2025-07-16 17:45:31,039 - utils.reliable_document_generator - INFO - SOW output path: C:/Users/<USER>/Documents/Test_Pace\Bobs name here  IR Investigation - SOW 20250716.docx
2025-07-16 17:45:31,039 - utils.reliable_document_generator - INFO - Using python-docx to replace placeholders in SOW document
2025-07-16 17:45:31,040 - utils.docx_placeholder_replacer - INFO - Resolved template path: templates/base_templates/dfir/SOW_IR_Template_single_price_IR_Investigation.docx
2025-07-16 17:45:31,041 - utils.docx_placeholder_replacer - INFO - Loading document: templates/base_templates/dfir/SOW_IR_Template_single_price_IR_Investigation.docx
2025-07-16 17:45:31,637 - utils.docx_placeholder_replacer - INFO - Saving document to: C:/Users/<USER>/Documents/Test_Pace\Bobs name here  IR Investigation - SOW 20250716.docx
2025-07-16 17:45:31,646 - utils.docx_placeholder_replacer - INFO - Document generated successfully: C:/Users/<USER>/Documents/Test_Pace\Bobs name here  IR Investigation - SOW 20250716.docx
2025-07-16 17:45:31,646 - utils.reliable_document_generator - INFO - SOW document created successfully with python-docx: C:/Users/<USER>/Documents/Test_Pace\Bobs name here  IR Investigation - SOW 20250716.docx
2025-07-16 17:45:31,646 - utils.reliable_document_generator - INFO - TACI SOW is needed
2025-07-16 17:45:31,647 - utils.reliable_document_generator - INFO - Generating TACI SOW...
2025-07-16 17:45:31,647 - utils.reliable_document_generator - INFO - TACI SOW template path: templates/base_templates/taci/SOW_Ransom_Consulting_template.docx
2025-07-16 17:45:31,647 - utils.reliable_document_generator - INFO - TACI SOW output path: C:/Users/<USER>/Documents/Test_Pace\Bobs name here  Ransom Communications - SOW 20250716.docx
2025-07-16 17:45:31,648 - utils.reliable_document_generator - INFO - Using python-docx to replace placeholders in TACI SOW document
2025-07-16 17:45:31,648 - utils.docx_placeholder_replacer - INFO - Resolved template path: templates/base_templates/taci/SOW_Ransom_Consulting_template.docx
2025-07-16 17:45:31,649 - utils.docx_placeholder_replacer - INFO - Loading document: templates/base_templates/taci/SOW_Ransom_Consulting_template.docx
2025-07-16 17:45:32,059 - utils.docx_placeholder_replacer - INFO - Saving document to: C:/Users/<USER>/Documents/Test_Pace\Bobs name here  Ransom Communications - SOW 20250716.docx
2025-07-16 17:45:34,615 - utils.docx_placeholder_replacer - INFO - Document generated successfully: C:/Users/<USER>/Documents/Test_Pace\Bobs name here  Ransom Communications - SOW 20250716.docx
2025-07-16 17:45:34,615 - utils.reliable_document_generator - INFO - TACI SOW document created successfully with python-docx: C:/Users/<USER>/Documents/Test_Pace\Bobs name here  Ransom Communications - SOW 20250716.docx
2025-07-16 17:45:34,616 - utils.reliable_document_generator - INFO - RR SOW is needed
2025-07-16 17:45:34,616 - utils.reliable_document_generator - INFO - Generating RR SOW...
2025-07-16 17:45:34,616 - utils.reliable_document_generator - INFO - RR SOW template path: templates/base_templates/rr/SOW_ON_Template_Recovery_&_Restoration_Support.docx
2025-07-16 17:45:34,616 - utils.reliable_document_generator - INFO - RR SOW output path: C:/Users/<USER>/Documents/Test_Pace\Bobs name here  Recovery & Remediation - SOW 20250716.docx
2025-07-16 17:45:34,616 - utils.reliable_document_generator - INFO - Using python-docx to replace placeholders in RR SOW document
2025-07-16 17:45:34,618 - utils.docx_placeholder_replacer - INFO - Resolved template path: templates/base_templates/rr/SOW_ON_Template_Recovery_&_Restoration_Support.docx
2025-07-16 17:45:34,618 - utils.docx_placeholder_replacer - INFO - Loading document: templates/base_templates/rr/SOW_ON_Template_Recovery_&_Restoration_Support.docx
2025-07-16 17:45:34,995 - utils.docx_placeholder_replacer - INFO - Saving document to: C:/Users/<USER>/Documents/Test_Pace\Bobs name here  Recovery & Remediation - SOW 20250716.docx
2025-07-16 17:45:35,002 - utils.docx_placeholder_replacer - INFO - Document generated successfully: C:/Users/<USER>/Documents/Test_Pace\Bobs name here  Recovery & Remediation - SOW 20250716.docx
2025-07-16 17:45:35,002 - utils.reliable_document_generator - INFO - RR SOW document created successfully with python-docx: C:/Users/<USER>/Documents/Test_Pace\Bobs name here  Recovery & Remediation - SOW 20250716.docx
2025-07-16 17:45:35,004 - utils.reliable_document_generator - INFO - Generating MSA...
2025-07-16 17:45:35,004 - utils.reliable_document_generator - INFO - MSA template path: templates/base_templates/msa_templates/Greenberg_Traurig_LLP_Master_Services_Agreement_Template.docx
2025-07-16 17:45:35,004 - utils.reliable_document_generator - INFO - MSA output path: C:/Users/<USER>/Documents/Test_Pace\Bobs name here  Master Services Agreement (MSA) 20250716.docx
2025-07-16 17:45:35,004 - utils.reliable_document_generator - INFO - Using python-docx to replace placeholders in MSA document
2025-07-16 17:45:35,005 - utils.docx_placeholder_replacer - INFO - Resolved template path: templates/base_templates/msa_templates/Greenberg_Traurig_LLP_Master_Services_Agreement_Template.docx
2025-07-16 17:45:35,005 - utils.docx_placeholder_replacer - INFO - Loading document: templates/base_templates/msa_templates/Greenberg_Traurig_LLP_Master_Services_Agreement_Template.docx
2025-07-16 17:45:35,456 - utils.docx_placeholder_replacer - INFO - Saving document to: C:/Users/<USER>/Documents/Test_Pace\Bobs name here  Master Services Agreement (MSA) 20250716.docx
2025-07-16 17:45:35,465 - utils.docx_placeholder_replacer - INFO - Document generated successfully: C:/Users/<USER>/Documents/Test_Pace\Bobs name here  Master Services Agreement (MSA) 20250716.docx
2025-07-16 17:45:35,465 - utils.reliable_document_generator - INFO - MSA document created successfully with python-docx: C:/Users/<USER>/Documents/Test_Pace\Bobs name here  Master Services Agreement (MSA) 20250716.docx
2025-07-16 17:45:35,466 - utils.reliable_document_generator - INFO - Generated 4 documents:
2025-07-16 17:45:35,466 - utils.reliable_document_generator - INFO - Document 1: C:/Users/<USER>/Documents/Test_Pace\Bobs name here  IR Investigation - SOW 20250716.docx
2025-07-16 17:45:35,466 - utils.reliable_document_generator - INFO - Document 2: C:/Users/<USER>/Documents/Test_Pace\Bobs name here  Ransom Communications - SOW 20250716.docx
2025-07-16 17:45:35,466 - utils.reliable_document_generator - INFO - Document 3: C:/Users/<USER>/Documents/Test_Pace\Bobs name here  Recovery & Remediation - SOW 20250716.docx
2025-07-16 17:45:35,467 - utils.reliable_document_generator - INFO - Document 4: C:/Users/<USER>/Documents/Test_Pace\Bobs name here  Master Services Agreement (MSA) 20250716.docx
2025-07-16 17:46:46,719 - utils.reliable_document_generator - INFO - Using output directory: C:/Users/<USER>/Documents/Test_Pace
2025-07-16 17:46:46,720 - utils.reliable_document_generator - INFO - ReliableDocumentGenerator initialized with output directory: C:/Users/<USER>/Documents/Test_Pace
2025-07-16 17:46:46,720 - utils.reliable_document_generator - INFO - Debugging template paths...
2025-07-16 17:46:46,720 - utils.reliable_document_generator - INFO - SOW template path: templates/base_templates/dfir/SOW_IR_Template_single_price_IR_Investigation.docx
2025-07-16 17:46:46,720 - utils.reliable_document_generator - INFO - Resolved SOW template path: templates/base_templates/dfir/SOW_IR_Template_single_price_IR_Investigation.docx
2025-07-16 17:46:46,720 - utils.reliable_document_generator - INFO - SOW template exists: True
2025-07-16 17:46:46,720 - utils.reliable_document_generator - INFO - MSA template path: templates/base_templates/msa_templates/Greenberg_Traurig_LLP_Master_Services_Agreement_Template.docx
2025-07-16 17:46:46,720 - utils.reliable_document_generator - INFO - Resolved MSA template path: templates/base_templates/msa_templates/Greenberg_Traurig_LLP_Master_Services_Agreement_Template.docx
2025-07-16 17:46:46,721 - utils.reliable_document_generator - INFO - MSA template exists: True
2025-07-16 17:46:46,721 - utils.reliable_document_generator - INFO - TACI SOW template path: templates/base_templates/taci/SOW_Ransom_Consulting_template.docx
2025-07-16 17:46:46,721 - utils.reliable_document_generator - INFO - Resolved TACI SOW template path: templates/base_templates/taci/SOW_Ransom_Consulting_template.docx
2025-07-16 17:46:46,721 - utils.reliable_document_generator - INFO - TACI SOW template exists: True
2025-07-16 17:46:46,721 - utils.reliable_document_generator - INFO - RR SOW template path: templates/base_templates/rr/SOW_ON_Template_Recovery_&_Restoration_Support.docx
2025-07-16 17:46:46,721 - utils.reliable_document_generator - INFO - Resolved RR SOW template path: templates/base_templates/rr/SOW_ON_Template_Recovery_&_Restoration_Support.docx
2025-07-16 17:46:46,721 - utils.reliable_document_generator - INFO - RR SOW template exists: True
2025-07-16 17:46:46,721 - utils.reliable_document_generator - INFO - All available templates:
2025-07-16 17:46:46,722 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\baa\Template_Business_Associate_Agreement.docx
2025-07-16 17:46:46,722 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\bec\SOW_Baker_GCP_BEC_template.docx
2025-07-16 17:46:46,722 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\bec\SOW_Baker_M365_BEC_template.docx
2025-07-16 17:46:46,722 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\bec\SOW_Chubb_Exchange_BEC_template.docx
2025-07-16 17:46:46,722 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\bec\SOW_Chubb_GCP_BEC_template.docx
2025-07-16 17:46:46,722 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\bec\SOW_Chubb_M365_BEC_template.docx
2025-07-16 17:46:46,722 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\bec\SOW_Coalition_GCP_BEC_template.docx
2025-07-16 17:46:46,722 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\bec\SOW_Coalition_M365_BEC_template.docx
2025-07-16 17:46:46,722 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\bec\SOW_Dykema_GCP_BEC_template.docx
2025-07-16 17:46:46,722 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\bec\SOW_Dykema_M365_BEC_template.docx
2025-07-16 17:46:46,722 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\bec\SOW_Exchange_BEC_template.docx
2025-07-16 17:46:46,722 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\bec\SOW_GCP_BEC_template.docx
2025-07-16 17:46:46,722 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\bec\SOW_M365_BEC_template.docx
2025-07-16 17:46:46,723 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\bec\SOW_Woods_M365_BEC_template.docx
2025-07-16 17:46:46,723 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\dfir\SOW_chubb_IR_cs_codes_template.docx
2025-07-16 17:46:46,723 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\dfir\SOW_Fixed_Fee_IR_template.docx
2025-07-16 17:46:46,723 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\dfir\SOW_IR_BRAP_Beckage_Verbiage_Template.docx
2025-07-16 17:46:46,723 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\dfir\SOW_IR_Template_IR Investigation_Beckage_Template.docx
2025-07-16 17:46:46,723 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\dfir\SOW_IR_Template_single_price_IR_Investigation.docx
2025-07-16 17:46:46,723 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\dfir\SOW_IR_Template_Tokio-Marine_Single Price_IR Investigation.docx
2025-07-16 17:46:46,723 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\dfir\SOW_IR_Template_Woods_Rogers_Only_IR_Investigation.docx
2025-07-16 17:46:46,723 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\dfir\SOW__IR_template_Beazley_FFP.docx
2025-07-16 17:46:46,723 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\dfir\SOW__IR_template_Beazley_FFP_EDR_New_Hybrid.docx
2025-07-16 17:46:46,723 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\dfir\SOW__IR_template_Beazley_FFP_EDR_New_M365.docx
2025-07-16 17:46:46,723 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\dpa\Template_Data_Processing_Agreement.docx
2025-07-16 17:46:46,723 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Baker_&_Hostetler_LLP_(2-Party)_Master_Services_Agreement_Template.docx
2025-07-16 17:46:46,723 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Baker_&_Hostetler_LLP_Beazley_TASB_Master_Services_Agreement_Template.docx
2025-07-16 17:46:46,725 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Baker_Donelson_Bearman_Caldwell_&_Berkowitz_P.C._Master_Services_Agreement_Template.docx
2025-07-16 17:46:46,725 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Cipriani_&_Werner_PC_Master_Services_Agreement_Template.docx
2025-07-16 17:46:46,725 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Clark_Hill_PLC_Master_Services_Agreement.docx
2025-07-16 17:46:46,726 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Constangy_Brooks_Smith_&_Prophete_LLP_Master_Services_Agreement_Template.docx
2025-07-16 17:46:46,726 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Dykema_Gossett_PLLC_Master_Service_Agreement_Template.docx
2025-07-16 17:46:46,726 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Eckert_Seamans_Cherin_&_Mellott_LLC_Master_Service_Agreement_Template.docx
2025-07-16 17:46:46,726 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Gordon_Rees_Scully_Mansukhani_LLP_Master_Services_Agreement_Template.docx
2025-07-16 17:46:46,726 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Greenberg_Traurig_LLP_Master_Services_Agreement_Template.docx
2025-07-16 17:46:46,727 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\IR_Services_2_Party_Master_Services_Agreement_Template.docx
2025-07-16 17:46:46,727 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\IR_Services_3_Party_Master_Services_Agreement_Template.docx
2025-07-16 17:46:46,727 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Jackson_Lewis_PC_Master_Services_Agreement_Template.docx
2025-07-16 17:46:46,727 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Jackson_Lewis_PC_with_BAA_Master_Services_Agreement_Template.docx
2025-07-16 17:46:46,727 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Lewis_Brisbois_Bisgaard_&_Smith_LLP_Master_Services_Agreement_Template.docx
2025-07-16 17:46:46,728 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Locke_Lord_Master_Services_Agreement_Template.docx
2025-07-16 17:46:46,728 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Maynard_Nexsen_PC_Master_Services_Agreement_Template.docx
2025-07-16 17:46:46,728 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\McDonald_Hopkins_LLC_Master_Services_Agreement_Template.docx
2025-07-16 17:46:46,728 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Mullen_Coughlin_LLC_Master_Services_Agreement_Template.docx
2025-07-16 17:46:46,728 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Nelson_Mullins_Riley_&_Scarborough_LLP_Master_Services_Agreement_Template.docx
2025-07-16 17:46:46,728 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Norton_Rose_Fulbright_Master_Services_Agreement_Template.docx
2025-07-16 17:46:46,728 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Octillo_Law_PLLC_Master_Services_Agreement_Template.docx
2025-07-16 17:46:46,729 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Ogletree_Deakins_Master_Services_Agreement_Template.docx
2025-07-16 17:46:46,729 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Ruskin_Moscou_Faltischek_P.C._Master_Services_Agreement_Template.docx
2025-07-16 17:46:46,729 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Shook_Hardy_&_Bacon_LLP_Master_Services_Agreement_Template.docx
2025-07-16 17:46:46,729 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\The_Beckage_Firm_PLLC_Master_Services_Agreement_Template.docx
2025-07-16 17:46:46,729 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\msa_templates\Woods_Rogers_Master_Services_Agreement_Template.docx
2025-07-16 17:46:46,730 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\rr\SOW_ON_Template_Beazley_Only_Recovery_&_Restoration_Support.docx
2025-07-16 17:46:46,730 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\rr\SOW_ON_Template_Recovery_&_Restoration_Support.docx
2025-07-16 17:46:46,730 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\taci\SOW_Coalition_Ransom_Consulting_FFP_template.docx
2025-07-16 17:46:46,730 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\taci\SOW_Ransom_Consulting_template.docx
2025-07-16 17:46:46,730 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\taci\SOW_RN-Template_Ransom_Site_Monitoring.docx
2025-07-16 17:46:46,730 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\taci\SOW_RN_Template_Beckage_Firm_Ransom_Consulting.docx
2025-07-16 17:46:46,730 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\taci\SOW_RN_Template_Chubb_Only_Ransom_Consulting.docx
2025-07-16 17:46:46,730 - utils.reliable_document_generator - INFO -   - D:\PACE_1.1.3\templates\base_templates\taci\SOW_RN_Template_Client_Data_Download.docx
2025-07-16 17:46:46,732 - utils.reliable_document_generator - INFO - Generating documents for DFIR engagement
2025-07-16 17:46:46,732 - utils.reliable_document_generator - INFO - Client: Test11
2025-07-16 17:46:46,733 - utils.reliable_document_generator - INFO - Law Firm: Greenberg Traurig, LLP
2025-07-16 17:46:46,733 - utils.reliable_document_generator - INFO - Output directory: C:/Users/<USER>/Documents/Test_Pace
2025-07-16 17:46:46,733 - utils.reliable_document_generator - INFO - Generating SOW...
2025-07-16 17:46:46,733 - utils.reliable_document_generator - INFO - SOW template path: templates/base_templates/dfir/SOW_IR_Template_single_price_IR_Investigation.docx
2025-07-16 17:46:46,733 - utils.reliable_document_generator - INFO - SOW output path: C:/Users/<USER>/Documents/Test_Pace\Test11 IR Investigation - SOW 20250716.docx
2025-07-16 17:46:46,734 - utils.reliable_document_generator - INFO - Using python-docx to replace placeholders in SOW document
2025-07-16 17:46:46,734 - utils.docx_placeholder_replacer - INFO - Resolved template path: templates/base_templates/dfir/SOW_IR_Template_single_price_IR_Investigation.docx
2025-07-16 17:46:46,734 - utils.docx_placeholder_replacer - INFO - Loading document: templates/base_templates/dfir/SOW_IR_Template_single_price_IR_Investigation.docx
2025-07-16 17:46:47,374 - utils.docx_placeholder_replacer - INFO - Saving document to: C:/Users/<USER>/Documents/Test_Pace\Test11 IR Investigation - SOW 20250716.docx
2025-07-16 17:46:47,393 - utils.docx_placeholder_replacer - INFO - Document generated successfully: C:/Users/<USER>/Documents/Test_Pace\Test11 IR Investigation - SOW 20250716.docx
2025-07-16 17:46:47,393 - utils.reliable_document_generator - INFO - SOW document created successfully with python-docx: C:/Users/<USER>/Documents/Test_Pace\Test11 IR Investigation - SOW 20250716.docx
2025-07-16 17:46:47,393 - utils.reliable_document_generator - INFO - TACI SOW is needed
2025-07-16 17:46:47,393 - utils.reliable_document_generator - INFO - Generating TACI SOW...
2025-07-16 17:46:47,393 - utils.reliable_document_generator - INFO - TACI SOW template path: templates/base_templates/taci/SOW_Ransom_Consulting_template.docx
2025-07-16 17:46:47,394 - utils.reliable_document_generator - INFO - TACI SOW output path: C:/Users/<USER>/Documents/Test_Pace\Test11 Ransom Communications - SOW 20250716.docx
2025-07-16 17:46:47,394 - utils.reliable_document_generator - INFO - Using python-docx to replace placeholders in TACI SOW document
2025-07-16 17:46:47,394 - utils.docx_placeholder_replacer - INFO - Resolved template path: templates/base_templates/taci/SOW_Ransom_Consulting_template.docx
2025-07-16 17:46:47,395 - utils.docx_placeholder_replacer - INFO - Loading document: templates/base_templates/taci/SOW_Ransom_Consulting_template.docx
2025-07-16 17:46:47,801 - utils.docx_placeholder_replacer - INFO - Saving document to: C:/Users/<USER>/Documents/Test_Pace\Test11 Ransom Communications - SOW 20250716.docx
2025-07-16 17:46:47,810 - utils.docx_placeholder_replacer - INFO - Document generated successfully: C:/Users/<USER>/Documents/Test_Pace\Test11 Ransom Communications - SOW 20250716.docx
2025-07-16 17:46:47,811 - utils.reliable_document_generator - INFO - TACI SOW document created successfully with python-docx: C:/Users/<USER>/Documents/Test_Pace\Test11 Ransom Communications - SOW 20250716.docx
2025-07-16 17:46:47,811 - utils.reliable_document_generator - INFO - RR SOW is needed
2025-07-16 17:46:47,811 - utils.reliable_document_generator - INFO - Generating RR SOW...
2025-07-16 17:46:47,811 - utils.reliable_document_generator - INFO - RR SOW template path: templates/base_templates/rr/SOW_ON_Template_Recovery_&_Restoration_Support.docx
2025-07-16 17:46:47,811 - utils.reliable_document_generator - INFO - RR SOW output path: C:/Users/<USER>/Documents/Test_Pace\Test11 Recovery & Remediation - SOW 20250716.docx
2025-07-16 17:46:47,812 - utils.reliable_document_generator - INFO - Using python-docx to replace placeholders in RR SOW document
2025-07-16 17:46:47,812 - utils.docx_placeholder_replacer - INFO - Resolved template path: templates/base_templates/rr/SOW_ON_Template_Recovery_&_Restoration_Support.docx
2025-07-16 17:46:47,813 - utils.docx_placeholder_replacer - INFO - Loading document: templates/base_templates/rr/SOW_ON_Template_Recovery_&_Restoration_Support.docx
2025-07-16 17:46:48,204 - utils.docx_placeholder_replacer - INFO - Saving document to: C:/Users/<USER>/Documents/Test_Pace\Test11 Recovery & Remediation - SOW 20250716.docx
2025-07-16 17:46:48,226 - utils.docx_placeholder_replacer - INFO - Document generated successfully: C:/Users/<USER>/Documents/Test_Pace\Test11 Recovery & Remediation - SOW 20250716.docx
2025-07-16 17:46:48,226 - utils.reliable_document_generator - INFO - RR SOW document created successfully with python-docx: C:/Users/<USER>/Documents/Test_Pace\Test11 Recovery & Remediation - SOW 20250716.docx
2025-07-16 17:46:48,227 - utils.reliable_document_generator - INFO - Generating MSA...
2025-07-16 17:46:48,227 - utils.reliable_document_generator - INFO - MSA template path: templates/base_templates/msa_templates/Greenberg_Traurig_LLP_Master_Services_Agreement_Template.docx
2025-07-16 17:46:48,228 - utils.reliable_document_generator - INFO - MSA output path: C:/Users/<USER>/Documents/Test_Pace\Test11 Master Services Agreement (MSA) 20250716.docx
2025-07-16 17:46:48,228 - utils.reliable_document_generator - INFO - Using python-docx to replace placeholders in MSA document
2025-07-16 17:46:48,228 - utils.docx_placeholder_replacer - INFO - Resolved template path: templates/base_templates/msa_templates/Greenberg_Traurig_LLP_Master_Services_Agreement_Template.docx
2025-07-16 17:46:48,229 - utils.docx_placeholder_replacer - INFO - Loading document: templates/base_templates/msa_templates/Greenberg_Traurig_LLP_Master_Services_Agreement_Template.docx
2025-07-16 17:46:48,718 - utils.docx_placeholder_replacer - INFO - Saving document to: C:/Users/<USER>/Documents/Test_Pace\Test11 Master Services Agreement (MSA) 20250716.docx
2025-07-16 17:46:48,730 - utils.docx_placeholder_replacer - INFO - Document generated successfully: C:/Users/<USER>/Documents/Test_Pace\Test11 Master Services Agreement (MSA) 20250716.docx
2025-07-16 17:46:48,730 - utils.reliable_document_generator - INFO - MSA document created successfully with python-docx: C:/Users/<USER>/Documents/Test_Pace\Test11 Master Services Agreement (MSA) 20250716.docx
2025-07-16 17:46:48,730 - utils.reliable_document_generator - INFO - Generated 4 documents:
2025-07-16 17:46:48,730 - utils.reliable_document_generator - INFO - Document 1: C:/Users/<USER>/Documents/Test_Pace\Test11 IR Investigation - SOW 20250716.docx
2025-07-16 17:46:48,731 - utils.reliable_document_generator - INFO - Document 2: C:/Users/<USER>/Documents/Test_Pace\Test11 Ransom Communications - SOW 20250716.docx
2025-07-16 17:46:48,731 - utils.reliable_document_generator - INFO - Document 3: C:/Users/<USER>/Documents/Test_Pace\Test11 Recovery & Remediation - SOW 20250716.docx
2025-07-16 17:46:48,731 - utils.reliable_document_generator - INFO - Document 4: C:/Users/<USER>/Documents/Test_Pace\Test11 Master Services Agreement (MSA) 20250716.docx
2025-07-16 21:09:38,942 - root - INFO - Starting PACE application
2025-07-16 21:09:38,942 - root - INFO - Python version: 3.12.7 (tags/v3.12.7:0b05ead, Oct  1 2024, 03:06:41) [MSC v.1941 64 bit (AMD64)]
2025-07-16 21:09:38,942 - root - INFO - Current directory: D:\PACE_1.1.3
2025-07-16 21:09:38,947 - root - INFO - Script directory: D:\PACE_1.1.3
2025-07-16 21:09:38,947 - root - INFO - Executable directory: D:\PACE_1.1.3\venv\Scripts
2025-07-16 21:09:39,043 - root - INFO - Checking for icon at: PACE.ico (exists: True)
2025-07-16 21:09:39,044 - root - INFO - Checking for icon at: D:\PACE_1.1.3\PACE.ico (exists: True)
2025-07-16 21:09:39,044 - root - INFO - Checking for icon at: D:\PACE_1.1.3\venv\Scripts\PACE.ico (exists: False)
2025-07-16 21:09:39,044 - root - INFO - Checking for icon at: app_icon.ico (exists: False)
2025-07-16 21:09:39,044 - root - INFO - Checking for icon at: D:\PACE_1.1.3\app_icon.ico (exists: False)
2025-07-16 21:09:39,044 - root - INFO - Checking for icon at: D:\PACE_1.1.3\venv\Scripts\app_icon.ico (exists: False)
2025-07-16 21:09:39,372 - root - INFO - Found and loaded icon from: PACE.ico
2025-07-16 21:09:41,236 - root - INFO - Main window created and shown successfully
2025-07-17 09:35:14,083 - root - INFO - Starting PACE application
2025-07-17 09:35:14,083 - root - INFO - Python version: 3.13.5 (tags/v3.13.5:6cb20a2, Jun 11 2025, 16:15:46) [MSC v.1943 64 bit (AMD64)]
2025-07-17 09:35:14,084 - root - INFO - Current directory: D:\PACE_1.1.3
2025-07-17 09:35:14,084 - root - INFO - Script directory: C:\Users\<USER>\AppData\Local\Temp\_MEI467922
2025-07-17 09:35:14,084 - root - INFO - Executable directory: D:\PACE_1.1.3\dist
2025-07-17 09:35:14,357 - root - INFO - Checking for icon at: PACE.ico (exists: True)
2025-07-17 09:35:14,358 - root - INFO - Checking for icon at: C:\Users\<USER>\AppData\Local\Temp\_MEI467922\PACE.ico (exists: False)
2025-07-17 09:35:14,358 - root - INFO - Checking for icon at: D:\PACE_1.1.3\dist\PACE.ico (exists: False)
2025-07-17 09:35:14,358 - root - INFO - Checking for icon at: app_icon.ico (exists: False)
2025-07-17 09:35:14,358 - root - INFO - Checking for icon at: C:\Users\<USER>\AppData\Local\Temp\_MEI467922\app_icon.ico (exists: False)
2025-07-17 09:35:14,358 - root - INFO - Checking for icon at: D:\PACE_1.1.3\dist\app_icon.ico (exists: False)
2025-07-17 09:35:14,398 - root - INFO - Found and loaded icon from: PACE.ico
2025-07-17 09:35:16,726 - root - INFO - Main window created and shown successfully
2025-07-18 08:56:32,745 - root - INFO - Starting PACE application
2025-07-18 08:56:32,745 - root - INFO - Python version: 3.13.5 (tags/v3.13.5:6cb20a2, Jun 11 2025, 16:15:46) [MSC v.1943 64 bit (AMD64)]
2025-07-18 08:56:32,745 - root - INFO - Current directory: D:\PACE_1.1.3
2025-07-18 08:56:32,745 - root - INFO - Script directory: C:\Users\<USER>\AppData\Local\Temp\_MEI332522
2025-07-18 08:56:32,745 - root - INFO - Executable directory: D:\PACE_1.1.3\dist
2025-07-18 08:56:33,026 - root - INFO - Checking for icon at: D:\PACE_1.1.3\dist\PACE.ico (exists: False)
2025-07-18 08:56:33,026 - root - INFO - Checking for icon at: C:\Users\<USER>\AppData\Local\Temp\_MEI332522\PACE.ico (exists: False)
2025-07-18 08:56:33,026 - root - INFO - Checking for icon at: app_icon.ico (exists: False)
2025-07-18 08:56:33,026 - root - INFO - Checking for icon at: C:\Users\<USER>\AppData\Local\Temp\_MEI332522\app_icon.ico (exists: False)
2025-07-18 08:56:33,026 - root - INFO - Checking for icon at: D:\PACE_1.1.3\dist\app_icon.ico (exists: False)
2025-07-18 08:56:33,026 - root - WARNING - Could not find application icon
2025-07-18 08:56:33,601 - root - INFO - Main window created and shown successfully
2025-07-18 08:57:59,115 - utils.reliable_document_generator - INFO - Using output directory: C:/Users/<USER>/Documents/Test_Pace
2025-07-18 08:57:59,116 - utils.reliable_document_generator - INFO - ReliableDocumentGenerator initialized with output directory: C:/Users/<USER>/Documents/Test_Pace
2025-07-18 08:57:59,116 - utils.reliable_document_generator - INFO - Debugging template paths...
2025-07-18 08:57:59,116 - utils.reliable_document_generator - INFO - SOW template path: templates/base_templates/dfir/SOW_IR_Template_single_price_IR_Investigation.docx
2025-07-18 08:57:59,116 - utils.reliable_document_generator - INFO - Resolved SOW template path: C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates/base_templates/dfir/SOW_IR_Template_single_price_IR_Investigation.docx
2025-07-18 08:57:59,116 - utils.reliable_document_generator - INFO - SOW template exists: True
2025-07-18 08:57:59,116 - utils.reliable_document_generator - INFO - MSA template path: templates/base_templates/msa_templates/Greenberg_Traurig_LLP_Master_Services_Agreement_Template.docx
2025-07-18 08:57:59,116 - utils.reliable_document_generator - INFO - Resolved MSA template path: C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates/base_templates/msa_templates/Greenberg_Traurig_LLP_Master_Services_Agreement_Template.docx
2025-07-18 08:57:59,117 - utils.reliable_document_generator - INFO - MSA template exists: True
2025-07-18 08:57:59,117 - utils.reliable_document_generator - INFO - TACI SOW template path: templates/base_templates/taci/SOW_Ransom_Consulting_template.docx
2025-07-18 08:57:59,117 - utils.reliable_document_generator - INFO - Resolved TACI SOW template path: C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates/base_templates/taci/SOW_Ransom_Consulting_template.docx
2025-07-18 08:57:59,117 - utils.reliable_document_generator - INFO - TACI SOW template exists: True
2025-07-18 08:57:59,118 - utils.reliable_document_generator - INFO - RR SOW template path: templates/base_templates/rr/SOW_ON_Template_Recovery_&_Restoration_Support.docx
2025-07-18 08:57:59,118 - utils.reliable_document_generator - INFO - Resolved RR SOW template path: C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates/base_templates/rr/SOW_ON_Template_Recovery_&_Restoration_Support.docx
2025-07-18 08:57:59,118 - utils.reliable_document_generator - INFO - RR SOW template exists: True
2025-07-18 08:57:59,118 - utils.reliable_document_generator - INFO - All available templates:
2025-07-18 08:57:59,120 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\baa\Template_Business_Associate_Agreement.docx
2025-07-18 08:57:59,120 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\bec\SOW_Baker_GCP_BEC_template.docx
2025-07-18 08:57:59,120 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\bec\SOW_Baker_M365_BEC_template.docx
2025-07-18 08:57:59,120 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\bec\SOW_Chubb_Exchange_BEC_template.docx
2025-07-18 08:57:59,120 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\bec\SOW_Chubb_GCP_BEC_template.docx
2025-07-18 08:57:59,120 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\bec\SOW_Chubb_M365_BEC_template.docx
2025-07-18 08:57:59,120 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\bec\SOW_Coalition_GCP_BEC_template.docx
2025-07-18 08:57:59,120 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\bec\SOW_Coalition_M365_BEC_template.docx
2025-07-18 08:57:59,120 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\bec\SOW_Dykema_GCP_BEC_template.docx
2025-07-18 08:57:59,120 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\bec\SOW_Dykema_M365_BEC_template.docx
2025-07-18 08:57:59,120 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\bec\SOW_Exchange_BEC_template.docx
2025-07-18 08:57:59,120 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\bec\SOW_GCP_BEC_template.docx
2025-07-18 08:57:59,121 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\bec\SOW_M365_BEC_template.docx
2025-07-18 08:57:59,121 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\bec\SOW_Woods_M365_BEC_template.docx
2025-07-18 08:57:59,121 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\dfir\SOW_chubb_IR_cs_codes_template.docx
2025-07-18 08:57:59,121 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\dfir\SOW_Fixed_Fee_IR_template.docx
2025-07-18 08:57:59,121 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\dfir\SOW_IR_BRAP_Beckage_Verbiage_Template.docx
2025-07-18 08:57:59,121 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\dfir\SOW_IR_Template_IR Investigation_Beckage_Template.docx
2025-07-18 08:57:59,121 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\dfir\SOW_IR_Template_single_price_IR_Investigation.docx
2025-07-18 08:57:59,121 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\dfir\SOW_IR_Template_Tokio-Marine_Single Price_IR Investigation.docx
2025-07-18 08:57:59,121 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\dfir\SOW_IR_Template_Woods_Rogers_Only_IR_Investigation.docx
2025-07-18 08:57:59,121 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\dfir\SOW__IR_template_Beazley_FFP.docx
2025-07-18 08:57:59,121 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\dfir\SOW__IR_template_Beazley_FFP_EDR_New_Hybrid.docx
2025-07-18 08:57:59,121 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\dfir\SOW__IR_template_Beazley_FFP_EDR_New_M365.docx
2025-07-18 08:57:59,121 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\dpa\Template_Data_Processing_Agreement.docx
2025-07-18 08:57:59,121 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\msa_templates\Baker_&_Hostetler_LLP_(2-Party)_Master_Services_Agreement_Template.docx
2025-07-18 08:57:59,121 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\msa_templates\Baker_&_Hostetler_LLP_Beazley_TASB_Master_Services_Agreement_Template.docx
2025-07-18 08:57:59,121 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\msa_templates\Baker_Donelson_Bearman_Caldwell_&_Berkowitz_P.C._Master_Services_Agreement_Template.docx
2025-07-18 08:57:59,122 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\msa_templates\Cipriani_&_Werner_PC_Master_Services_Agreement_Template.docx
2025-07-18 08:57:59,122 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\msa_templates\Clark_Hill_PLC_Master_Services_Agreement.docx
2025-07-18 08:57:59,122 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\msa_templates\Constangy_Brooks_Smith_&_Prophete_LLP_Master_Services_Agreement_Template.docx
2025-07-18 08:57:59,122 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\msa_templates\Dykema_Gossett_PLLC_Master_Service_Agreement_Template.docx
2025-07-18 08:57:59,122 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\msa_templates\Eckert_Seamans_Cherin_&_Mellott_LLC_Master_Service_Agreement_Template.docx
2025-07-18 08:57:59,122 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\msa_templates\Gordon_Rees_Scully_Mansukhani_LLP_Master_Services_Agreement_Template.docx
2025-07-18 08:57:59,122 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\msa_templates\Greenberg_Traurig_LLP_Master_Services_Agreement_Template.docx
2025-07-18 08:57:59,122 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\msa_templates\IR_Services_2_Party_Master_Services_Agreement_Template.docx
2025-07-18 08:57:59,122 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\msa_templates\IR_Services_3_Party_Master_Services_Agreement_Template.docx
2025-07-18 08:57:59,122 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\msa_templates\Jackson_Lewis_PC_Master_Services_Agreement_Template.docx
2025-07-18 08:57:59,122 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\msa_templates\Jackson_Lewis_PC_with_BAA_Master_Services_Agreement_Template.docx
2025-07-18 08:57:59,122 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\msa_templates\Lewis_Brisbois_Bisgaard_&_Smith_LLP_Master_Services_Agreement_Template.docx
2025-07-18 08:57:59,122 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\msa_templates\Locke_Lord_Master_Services_Agreement_Template.docx
2025-07-18 08:57:59,122 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\msa_templates\Maynard_Nexsen_PC_Master_Services_Agreement_Template.docx
2025-07-18 08:57:59,122 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\msa_templates\McDonald_Hopkins_LLC_Master_Services_Agreement_Template.docx
2025-07-18 08:57:59,122 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\msa_templates\Mullen_Coughlin_LLC_Master_Services_Agreement_Template.docx
2025-07-18 08:57:59,122 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\msa_templates\Nelson_Mullins_Riley_&_Scarborough_LLP_Master_Services_Agreement_Template.docx
2025-07-18 08:57:59,123 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\msa_templates\Norton_Rose_Fulbright_Master_Services_Agreement_Template.docx
2025-07-18 08:57:59,123 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\msa_templates\Octillo_Law_PLLC_Master_Services_Agreement_Template.docx
2025-07-18 08:57:59,123 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\msa_templates\Ogletree_Deakins_Master_Services_Agreement_Template.docx
2025-07-18 08:57:59,123 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\msa_templates\Ruskin_Moscou_Faltischek_P.C._Master_Services_Agreement_Template.docx
2025-07-18 08:57:59,123 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\msa_templates\Shook_Hardy_&_Bacon_LLP_Master_Services_Agreement_Template.docx
2025-07-18 08:57:59,123 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\msa_templates\The_Beckage_Firm_PLLC_Master_Services_Agreement_Template.docx
2025-07-18 08:57:59,123 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\msa_templates\Woods_Rogers_Master_Services_Agreement_Template.docx
2025-07-18 08:57:59,123 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\rr\SOW_ON_Template_Beazley_Only_Recovery_&_Restoration_Support.docx
2025-07-18 08:57:59,123 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\rr\SOW_ON_Template_Recovery_&_Restoration_Support.docx
2025-07-18 08:57:59,123 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\taci\SOW_Coalition_Ransom_Consulting_FFP_template.docx
2025-07-18 08:57:59,123 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\taci\SOW_Ransom_Consulting_template.docx
2025-07-18 08:57:59,123 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\taci\SOW_RN-Template_Ransom_Site_Monitoring.docx
2025-07-18 08:57:59,123 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\taci\SOW_RN_Template_Beckage_Firm_Ransom_Consulting.docx
2025-07-18 08:57:59,123 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\taci\SOW_RN_Template_Chubb_Only_Ransom_Consulting.docx
2025-07-18 08:57:59,123 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates\base_templates\taci\SOW_RN_Template_Client_Data_Download.docx
2025-07-18 08:57:59,125 - utils.reliable_document_generator - INFO - Generating documents for DFIR engagement
2025-07-18 08:57:59,125 - utils.reliable_document_generator - INFO - Client: Testing Again
2025-07-18 08:57:59,125 - utils.reliable_document_generator - INFO - Law Firm: Greenberg Traurig, LLP
2025-07-18 08:57:59,125 - utils.reliable_document_generator - INFO - Output directory: C:/Users/<USER>/Documents/Test_Pace
2025-07-18 08:57:59,125 - utils.reliable_document_generator - INFO - Generating SOW...
2025-07-18 08:57:59,126 - utils.reliable_document_generator - INFO - SOW template path: templates/base_templates/dfir/SOW_IR_Template_single_price_IR_Investigation.docx
2025-07-18 08:57:59,126 - utils.reliable_document_generator - INFO - SOW output path: C:/Users/<USER>/Documents/Test_Pace\Testing Again IR Investigation - SOW 20250718.docx
2025-07-18 08:57:59,126 - utils.reliable_document_generator - INFO - Using python-docx to replace placeholders in SOW document
2025-07-18 08:57:59,127 - utils.docx_placeholder_replacer - INFO - Resolved template path: C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates/base_templates/dfir/SOW_IR_Template_single_price_IR_Investigation.docx
2025-07-18 08:57:59,127 - utils.docx_placeholder_replacer - INFO - Loading document: C:\Users\<USER>\AppData\Local\Temp\_MEI332522\templates/base_templates/dfir/SOW_IR_Template_single_price_IR_Investigation.docx
