@echo off
echo PACE Directory Cleanup
echo =====================

echo Cleaning up test files...
if exist "test_bold_formatting_fix.py" del /f /q "test_bold_formatting_fix.py"
if exist "test_build_readiness.py" del /f /q "test_build_readiness.py"
if exist "test_complete_fixes.py" del /f /q "test_complete_fixes.py"
if exist "test_comprehensive_pricing.py" del /f /q "test_comprehensive_pricing.py"
if exist "test_document_generation.py" del /f /q "test_document_generation.py"
if exist "test_executable.py" del /f /q "test_executable.py"
if exist "test_garamond_fix.py" del /f /q "test_garamond_fix.py"
if exist "test_greenberg_font_fix.py" del /f /q "test_greenberg_font_fix.py"
if exist "test_icon_build.py" del /f /q "test_icon_build.py"
if exist "test_icon_display.py" del /f /q "test_icon_display.py"
if exist "test_legal_date_fix.py" del /f /q "test_legal_date_fix.py"
if exist "test_no_italics_fix.py" del /f /q "test_no_italics_fix.py"
if exist "test_pricing_calculations.py" del /f /q "test_pricing_calculations.py"
if exist "test_readme_fix.py" del /f /q "test_readme_fix.py"
if exist "test_rr_improvements.py" del /f /q "test_rr_improvements.py"
if exist "test_todays_updates.py" del /f /q "test_todays_updates.py"
if exist "test_version_update.py" del /f /q "test_version_update.py"
if exist "debug_greenberg_msa.py" del /f /q "debug_greenberg_msa.py"
if exist "check_generated_formatting.py" del /f /q "check_generated_formatting.py"
if exist "check_template_formatting.py" del /f /q "check_template_formatting.py"
if exist "fix_greenberg_fonts.py" del /f /q "fix_greenberg_fonts.py"
if exist "quick_test.py" del /f /q "quick_test.py"
if exist "Greenberg_MSA_Garamond_Test_20250718_085217.docx" del /f /q "Greenberg_MSA_Garamond_Test_20250718_085217.docx"
if exist "Greenberg_MSA_NoItalics_Test_20250718_090308.docx" del /f /q "Greenberg_MSA_NoItalics_Test_20250718_090308.docx"
if exist "Greenberg_MSA_Test_20250718_084059.docx" del /f /q "Greenberg_MSA_Test_20250718_084059.docx"
if exist "test_greenberg_msa_fixed.docx" del /f /q "test_greenberg_msa_fixed.docx"
if exist "test_greenberg_msa_with_font_fix.docx" del /f /q "test_greenberg_msa_with_font_fix.docx"
if exist "document_generation_debug.log" del /f /q "document_generation_debug.log"
if exist "reliable_document_generation.log" del /f /q "reliable_document_generation.log"
if exist "create_icon.py" del /f /q "create_icon.py"
if exist "create_original_pace_icon.py" del /f /q "create_original_pace_icon.py"
if exist "create_proper_icon.py" del /f /q "create_proper_icon.py"
if exist "PACE_icon_preview.png" del /f /q "PACE_icon_preview.png"
if exist "build/" rmdir /s /q "build/"
if exist "__pycache__/" rmdir /s /q "__pycache__/"
if exist "distribution/" rmdir /s /q "distribution/"
if exist "build_auto_installer.bat" del /f /q "build_auto_installer.bat"
if exist "build_complete.bat" del /f /q "build_complete.bat"
if exist "build_for_team.bat" del /f /q "build_for_team.bat"
if exist "build_pace.bat" del /f /q "build_pace.bat"
if exist "build_installer.bat" del /f /q "build_installer.bat"
if exist "DEPLOYMENT_GUIDE.md" del /f /q "DEPLOYMENT_GUIDE.md"
if exist "DEPLOYMENT_SUMMARY.md" del /f /q "DEPLOYMENT_SUMMARY.md"
if exist "TEAM_DISTRIBUTION_GUIDE.md" del /f /q "TEAM_DISTRIBUTION_GUIDE.md"
if exist "README.txt" del /f /q "README.txt"

echo Cleanup complete!
echo.
echo Remaining files are essential for PACE operation.
pause
