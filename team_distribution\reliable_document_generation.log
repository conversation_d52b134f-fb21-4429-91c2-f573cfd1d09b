2025-07-17 10:08:15,165 - root - INFO - Starting PACE application
2025-07-17 10:08:15,168 - root - INFO - Python version: 3.13.5 (tags/v3.13.5:6cb20a2, Jun 11 2025, 16:15:46) [MSC v.1943 64 bit (AMD64)]
2025-07-17 10:08:15,170 - root - INFO - Current directory: D:\PACE_1.1.3\team_distribution
2025-07-17 10:08:15,171 - root - INFO - Script directory: C:\Users\<USER>\AppData\Local\Temp\_MEI326922
2025-07-17 10:08:15,172 - root - INFO - Executable directory: D:\PACE_1.1.3\team_distribution
2025-07-17 10:08:15,514 - root - INFO - Checking for icon at: PACE.ico (exists: False)
2025-07-17 10:08:15,516 - root - INFO - Checking for icon at: C:\Users\<USER>\AppData\Local\Temp\_MEI326922\PACE.ico (exists: False)
2025-07-17 10:08:15,519 - root - INFO - Checking for icon at: D:\PACE_1.1.3\team_distribution\PACE.ico (exists: False)
2025-07-17 10:08:15,520 - root - INFO - Checking for icon at: app_icon.ico (exists: False)
2025-07-17 10:08:15,521 - root - INFO - Checking for icon at: C:\Users\<USER>\AppData\Local\Temp\_MEI326922\app_icon.ico (exists: False)
2025-07-17 10:08:15,522 - root - INFO - Checking for icon at: D:\PACE_1.1.3\team_distribution\app_icon.ico (exists: False)
2025-07-17 10:08:15,524 - root - WARNING - Could not find application icon
2025-07-17 10:08:17,577 - root - INFO - Main window created and shown successfully
2025-07-17 10:09:56,667 - utils.reliable_document_generator - INFO - Using output directory: C:/Users/<USER>/Documents/Test_Pace
2025-07-17 10:09:56,668 - utils.reliable_document_generator - INFO - ReliableDocumentGenerator initialized with output directory: C:/Users/<USER>/Documents/Test_Pace
2025-07-17 10:09:56,670 - utils.reliable_document_generator - INFO - Debugging template paths...
2025-07-17 10:09:56,670 - utils.reliable_document_generator - INFO - SOW template path: templates/base_templates/dfir/SOW_IR_Template_single_price_IR_Investigation.docx
2025-07-17 10:09:56,672 - utils.reliable_document_generator - INFO - Resolved SOW template path: C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates/base_templates/dfir/SOW_IR_Template_single_price_IR_Investigation.docx
2025-07-17 10:09:56,673 - utils.reliable_document_generator - INFO - SOW template exists: True
2025-07-17 10:09:56,674 - utils.reliable_document_generator - INFO - MSA template path: templates/base_templates/msa_templates/Greenberg_Traurig_LLP_Master_Services_Agreement_Template.docx
2025-07-17 10:09:56,675 - utils.reliable_document_generator - INFO - Resolved MSA template path: C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates/base_templates/msa_templates/Greenberg_Traurig_LLP_Master_Services_Agreement_Template.docx
2025-07-17 10:09:56,675 - utils.reliable_document_generator - INFO - MSA template exists: True
2025-07-17 10:09:56,676 - utils.reliable_document_generator - INFO - TACI SOW template path: templates/base_templates/taci/SOW_Ransom_Consulting_template.docx
2025-07-17 10:09:56,676 - utils.reliable_document_generator - INFO - Resolved TACI SOW template path: C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates/base_templates/taci/SOW_Ransom_Consulting_template.docx
2025-07-17 10:09:56,676 - utils.reliable_document_generator - INFO - TACI SOW template exists: True
2025-07-17 10:09:56,677 - utils.reliable_document_generator - INFO - RR SOW template path: templates/base_templates/rr/SOW_ON_Template_Recovery_&_Restoration_Support.docx
2025-07-17 10:09:56,678 - utils.reliable_document_generator - INFO - Resolved RR SOW template path: C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates/base_templates/rr/SOW_ON_Template_Recovery_&_Restoration_Support.docx
2025-07-17 10:09:56,679 - utils.reliable_document_generator - INFO - RR SOW template exists: True
2025-07-17 10:09:56,680 - utils.reliable_document_generator - INFO - All available templates:
2025-07-17 10:09:56,682 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\baa\Template_Business_Associate_Agreement.docx
2025-07-17 10:09:56,684 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\bec\SOW_Baker_GCP_BEC_template.docx
2025-07-17 10:09:56,685 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\bec\SOW_Baker_M365_BEC_template.docx
2025-07-17 10:09:56,685 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\bec\SOW_Chubb_Exchange_BEC_template.docx
2025-07-17 10:09:56,686 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\bec\SOW_Chubb_GCP_BEC_template.docx
2025-07-17 10:09:56,687 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\bec\SOW_Chubb_M365_BEC_template.docx
2025-07-17 10:09:56,688 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\bec\SOW_Coalition_GCP_BEC_template.docx
2025-07-17 10:09:56,689 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\bec\SOW_Coalition_M365_BEC_template.docx
2025-07-17 10:09:56,689 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\bec\SOW_Dykema_GCP_BEC_template.docx
2025-07-17 10:09:56,690 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\bec\SOW_Dykema_M365_BEC_template.docx
2025-07-17 10:09:56,690 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\bec\SOW_Exchange_BEC_template.docx
2025-07-17 10:09:56,691 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\bec\SOW_GCP_BEC_template.docx
2025-07-17 10:09:56,691 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\bec\SOW_M365_BEC_template.docx
2025-07-17 10:09:56,691 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\bec\SOW_Woods_M365_BEC_template.docx
2025-07-17 10:09:56,692 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\dfir\SOW_chubb_IR_cs_codes_template.docx
2025-07-17 10:09:56,692 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\dfir\SOW_Fixed_Fee_IR_template.docx
2025-07-17 10:09:56,693 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\dfir\SOW_IR_BRAP_Beckage_Verbiage_Template.docx
2025-07-17 10:09:56,693 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\dfir\SOW_IR_Template_IR Investigation_Beckage_Template.docx
2025-07-17 10:09:56,693 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\dfir\SOW_IR_Template_single_price_IR_Investigation.docx
2025-07-17 10:09:56,694 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\dfir\SOW_IR_Template_Tokio-Marine_Single Price_IR Investigation.docx
2025-07-17 10:09:56,694 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\dfir\SOW_IR_Template_Woods_Rogers_Only_IR_Investigation.docx
2025-07-17 10:09:56,695 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\dfir\SOW__IR_template_Beazley_FFP.docx
2025-07-17 10:09:56,695 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\dfir\SOW__IR_template_Beazley_FFP_EDR_New_Hybrid.docx
2025-07-17 10:09:56,696 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\dfir\SOW__IR_template_Beazley_FFP_EDR_New_M365.docx
2025-07-17 10:09:56,696 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\dpa\Template_Data_Processing_Agreement.docx
2025-07-17 10:09:56,697 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\msa_templates\Baker_&_Hostetler_LLP_(2-Party)_Master_Services_Agreement_Template.docx
2025-07-17 10:09:56,697 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\msa_templates\Baker_&_Hostetler_LLP_Beazley_TASB_Master_Services_Agreement_Template.docx
2025-07-17 10:09:56,698 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\msa_templates\Baker_Donelson_Bearman_Caldwell_&_Berkowitz_P.C._Master_Services_Agreement_Template.docx
2025-07-17 10:09:56,698 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\msa_templates\Cipriani_&_Werner_PC_Master_Services_Agreement_Template.docx
2025-07-17 10:09:56,700 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\msa_templates\Clark_Hill_PLC_Master_Services_Agreement.docx
2025-07-17 10:09:56,700 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\msa_templates\Constangy_Brooks_Smith_&_Prophete_LLP_Master_Services_Agreement_Template.docx
2025-07-17 10:09:56,701 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\msa_templates\Dykema_Gossett_PLLC_Master_Service_Agreement_Template.docx
2025-07-17 10:09:56,702 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\msa_templates\Eckert_Seamans_Cherin_&_Mellott_LLC_Master_Service_Agreement_Template.docx
2025-07-17 10:09:56,702 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\msa_templates\Gordon_Rees_Scully_Mansukhani_LLP_Master_Services_Agreement_Template.docx
2025-07-17 10:09:56,703 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\msa_templates\Greenberg_Traurig_LLP_Master_Services_Agreement_Template.docx
2025-07-17 10:09:56,703 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\msa_templates\IR_Services_2_Party_Master_Services_Agreement_Template.docx
2025-07-17 10:09:56,703 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\msa_templates\IR_Services_3_Party_Master_Services_Agreement_Template.docx
2025-07-17 10:09:56,704 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\msa_templates\Jackson_Lewis_PC_Master_Services_Agreement_Template.docx
2025-07-17 10:09:56,704 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\msa_templates\Jackson_Lewis_PC_with_BAA_Master_Services_Agreement_Template.docx
2025-07-17 10:09:56,705 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\msa_templates\Lewis_Brisbois_Bisgaard_&_Smith_LLP_Master_Services_Agreement_Template.docx
2025-07-17 10:09:56,705 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\msa_templates\Locke_Lord_Master_Services_Agreement_Template.docx
2025-07-17 10:09:56,705 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\msa_templates\Maynard_Nexsen_PC_Master_Services_Agreement_Template.docx
2025-07-17 10:09:56,706 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\msa_templates\McDonald_Hopkins_LLC_Master_Services_Agreement_Template.docx
2025-07-17 10:09:56,707 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\msa_templates\Mullen_Coughlin_LLC_Master_Services_Agreement_Template.docx
2025-07-17 10:09:56,707 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\msa_templates\Nelson_Mullins_Riley_&_Scarborough_LLP_Master_Services_Agreement_Template.docx
2025-07-17 10:09:56,708 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\msa_templates\Norton_Rose_Fulbright_Master_Services_Agreement_Template.docx
2025-07-17 10:09:56,708 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\msa_templates\Octillo_Law_PLLC_Master_Services_Agreement_Template.docx
2025-07-17 10:09:56,708 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\msa_templates\Ogletree_Deakins_Master_Services_Agreement_Template.docx
2025-07-17 10:09:56,709 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\msa_templates\Ruskin_Moscou_Faltischek_P.C._Master_Services_Agreement_Template.docx
2025-07-17 10:09:56,710 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\msa_templates\Shook_Hardy_&_Bacon_LLP_Master_Services_Agreement_Template.docx
2025-07-17 10:09:56,711 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\msa_templates\The_Beckage_Firm_PLLC_Master_Services_Agreement_Template.docx
2025-07-17 10:09:56,711 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\msa_templates\Woods_Rogers_Master_Services_Agreement_Template.docx
2025-07-17 10:09:56,711 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\rr\SOW_ON_Template_Beazley_Only_Recovery_&_Restoration_Support.docx
2025-07-17 10:09:56,712 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\rr\SOW_ON_Template_Recovery_&_Restoration_Support.docx
2025-07-17 10:09:56,712 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\taci\SOW_Coalition_Ransom_Consulting_FFP_template.docx
2025-07-17 10:09:56,712 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\taci\SOW_Ransom_Consulting_template.docx
2025-07-17 10:09:56,713 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\taci\SOW_RN-Template_Ransom_Site_Monitoring.docx
2025-07-17 10:09:56,713 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\taci\SOW_RN_Template_Beckage_Firm_Ransom_Consulting.docx
2025-07-17 10:09:56,713 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\taci\SOW_RN_Template_Chubb_Only_Ransom_Consulting.docx
2025-07-17 10:09:56,714 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates\base_templates\taci\SOW_RN_Template_Client_Data_Download.docx
2025-07-17 10:09:56,725 - utils.reliable_document_generator - INFO - Generating documents for DFIR engagement
2025-07-17 10:09:56,727 - utils.reliable_document_generator - INFO - Client: Test1234
2025-07-17 10:09:56,727 - utils.reliable_document_generator - INFO - Law Firm: Greenberg Traurig, LLP
2025-07-17 10:09:56,728 - utils.reliable_document_generator - INFO - Output directory: C:/Users/<USER>/Documents/Test_Pace
2025-07-17 10:09:56,728 - utils.reliable_document_generator - INFO - Generating SOW...
2025-07-17 10:09:56,728 - utils.reliable_document_generator - INFO - SOW template path: templates/base_templates/dfir/SOW_IR_Template_single_price_IR_Investigation.docx
2025-07-17 10:09:56,729 - utils.reliable_document_generator - INFO - SOW output path: C:/Users/<USER>/Documents/Test_Pace\Test1234 IR Investigation - SOW 20250717.docx
2025-07-17 10:09:56,730 - utils.reliable_document_generator - INFO - Using python-docx to replace placeholders in SOW document
2025-07-17 10:09:56,730 - utils.docx_placeholder_replacer - INFO - Resolved template path: C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates/base_templates/dfir/SOW_IR_Template_single_price_IR_Investigation.docx
2025-07-17 10:09:56,731 - utils.docx_placeholder_replacer - INFO - Loading document: C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates/base_templates/dfir/SOW_IR_Template_single_price_IR_Investigation.docx
2025-07-17 10:09:57,334 - utils.docx_placeholder_replacer - INFO - Saving document to: C:/Users/<USER>/Documents/Test_Pace\Test1234 IR Investigation - SOW 20250717.docx
2025-07-17 10:09:57,381 - utils.docx_placeholder_replacer - INFO - Document generated successfully: C:/Users/<USER>/Documents/Test_Pace\Test1234 IR Investigation - SOW 20250717.docx
2025-07-17 10:09:57,382 - utils.reliable_document_generator - INFO - SOW document created successfully with python-docx: C:/Users/<USER>/Documents/Test_Pace\Test1234 IR Investigation - SOW 20250717.docx
2025-07-17 10:09:57,383 - utils.reliable_document_generator - INFO - TACI SOW is needed
2025-07-17 10:09:57,383 - utils.reliable_document_generator - INFO - Generating TACI SOW...
2025-07-17 10:09:57,384 - utils.reliable_document_generator - INFO - TACI SOW template path: templates/base_templates/taci/SOW_Ransom_Consulting_template.docx
2025-07-17 10:09:57,384 - utils.reliable_document_generator - INFO - TACI SOW output path: C:/Users/<USER>/Documents/Test_Pace\Test1234 Ransom Communications - SOW 20250717.docx
2025-07-17 10:09:57,385 - utils.reliable_document_generator - INFO - Using python-docx to replace placeholders in TACI SOW document
2025-07-17 10:09:57,386 - utils.docx_placeholder_replacer - INFO - Resolved template path: C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates/base_templates/taci/SOW_Ransom_Consulting_template.docx
2025-07-17 10:09:57,387 - utils.docx_placeholder_replacer - INFO - Loading document: C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates/base_templates/taci/SOW_Ransom_Consulting_template.docx
2025-07-17 10:09:57,771 - utils.docx_placeholder_replacer - INFO - Saving document to: C:/Users/<USER>/Documents/Test_Pace\Test1234 Ransom Communications - SOW 20250717.docx
2025-07-17 10:09:57,801 - utils.docx_placeholder_replacer - INFO - Document generated successfully: C:/Users/<USER>/Documents/Test_Pace\Test1234 Ransom Communications - SOW 20250717.docx
2025-07-17 10:09:57,802 - utils.reliable_document_generator - INFO - TACI SOW document created successfully with python-docx: C:/Users/<USER>/Documents/Test_Pace\Test1234 Ransom Communications - SOW 20250717.docx
2025-07-17 10:09:57,802 - utils.reliable_document_generator - INFO - RR SOW document generation requested
2025-07-17 10:09:57,803 - utils.reliable_document_generator - INFO - Generating RR SOW...
2025-07-17 10:09:57,803 - utils.reliable_document_generator - INFO - RR SOW template path: templates/base_templates/rr/SOW_ON_Template_Recovery_&_Restoration_Support.docx
2025-07-17 10:09:57,804 - utils.reliable_document_generator - INFO - RR SOW output path: C:/Users/<USER>/Documents/Test_Pace\Test1234 Recovery & Remediation - SOW 20250717.docx
2025-07-17 10:09:57,805 - utils.reliable_document_generator - INFO - Using python-docx to replace placeholders in RR SOW document
2025-07-17 10:09:57,805 - utils.docx_placeholder_replacer - INFO - Resolved template path: C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates/base_templates/rr/SOW_ON_Template_Recovery_&_Restoration_Support.docx
2025-07-17 10:09:57,806 - utils.docx_placeholder_replacer - INFO - Loading document: C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates/base_templates/rr/SOW_ON_Template_Recovery_&_Restoration_Support.docx
2025-07-17 10:09:58,206 - utils.docx_placeholder_replacer - INFO - Saving document to: C:/Users/<USER>/Documents/Test_Pace\Test1234 Recovery & Remediation - SOW 20250717.docx
2025-07-17 10:09:58,221 - utils.docx_placeholder_replacer - INFO - Document generated successfully: C:/Users/<USER>/Documents/Test_Pace\Test1234 Recovery & Remediation - SOW 20250717.docx
2025-07-17 10:09:58,221 - utils.reliable_document_generator - INFO - RR SOW document created successfully with python-docx: C:/Users/<USER>/Documents/Test_Pace\Test1234 Recovery & Remediation - SOW 20250717.docx
2025-07-17 10:09:58,222 - utils.reliable_document_generator - INFO - Generating MSA...
2025-07-17 10:09:58,222 - utils.reliable_document_generator - INFO - MSA template path: templates/base_templates/msa_templates/Greenberg_Traurig_LLP_Master_Services_Agreement_Template.docx
2025-07-17 10:09:58,222 - utils.reliable_document_generator - INFO - MSA output path: C:/Users/<USER>/Documents/Test_Pace\Test1234 Master Services Agreement (MSA) 20250717.docx
2025-07-17 10:09:58,223 - utils.reliable_document_generator - INFO - Using python-docx to replace placeholders in MSA document
2025-07-17 10:09:58,223 - utils.docx_placeholder_replacer - INFO - Resolved template path: C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates/base_templates/msa_templates/Greenberg_Traurig_LLP_Master_Services_Agreement_Template.docx
2025-07-17 10:09:58,224 - utils.docx_placeholder_replacer - INFO - Loading document: C:\Users\<USER>\AppData\Local\Temp\_MEI326922\templates/base_templates/msa_templates/Greenberg_Traurig_LLP_Master_Services_Agreement_Template.docx
2025-07-17 10:09:58,681 - utils.docx_placeholder_replacer - INFO - Saving document to: C:/Users/<USER>/Documents/Test_Pace\Test1234 Master Services Agreement (MSA) 20250717.docx
2025-07-17 10:09:58,704 - utils.docx_placeholder_replacer - INFO - Document generated successfully: C:/Users/<USER>/Documents/Test_Pace\Test1234 Master Services Agreement (MSA) 20250717.docx
2025-07-17 10:09:58,705 - utils.reliable_document_generator - INFO - MSA document created successfully with python-docx: C:/Users/<USER>/Documents/Test_Pace\Test1234 Master Services Agreement (MSA) 20250717.docx
2025-07-17 10:09:58,705 - utils.reliable_document_generator - INFO - Generated 4 documents:
2025-07-17 10:09:58,705 - utils.reliable_document_generator - INFO - Document 1: C:/Users/<USER>/Documents/Test_Pace\Test1234 IR Investigation - SOW 20250717.docx
2025-07-17 10:09:58,706 - utils.reliable_document_generator - INFO - Document 2: C:/Users/<USER>/Documents/Test_Pace\Test1234 Ransom Communications - SOW 20250717.docx
2025-07-17 10:09:58,706 - utils.reliable_document_generator - INFO - Document 3: C:/Users/<USER>/Documents/Test_Pace\Test1234 Recovery & Remediation - SOW 20250717.docx
2025-07-17 10:09:58,706 - utils.reliable_document_generator - INFO - Document 4: C:/Users/<USER>/Documents/Test_Pace\Test1234 Master Services Agreement (MSA) 20250717.docx
2025-07-17 10:28:56,543 - root - INFO - Starting PACE application
2025-07-17 10:28:56,544 - root - INFO - Python version: 3.13.5 (tags/v3.13.5:6cb20a2, Jun 11 2025, 16:15:46) [MSC v.1943 64 bit (AMD64)]
2025-07-17 10:28:56,545 - root - INFO - Current directory: D:\PACE_1.1.3\team_distribution
2025-07-17 10:28:56,545 - root - INFO - Script directory: C:\Users\<USER>\AppData\Local\Temp\_MEI101082
2025-07-17 10:28:56,545 - root - INFO - Executable directory: D:\PACE_1.1.3\team_distribution
2025-07-17 10:28:56,872 - root - INFO - Checking for icon at: PACE.ico (exists: False)
2025-07-17 10:28:56,873 - root - INFO - Checking for icon at: C:\Users\<USER>\AppData\Local\Temp\_MEI101082\PACE.ico (exists: False)
2025-07-17 10:28:56,873 - root - INFO - Checking for icon at: D:\PACE_1.1.3\team_distribution\PACE.ico (exists: False)
2025-07-17 10:28:56,874 - root - INFO - Checking for icon at: app_icon.ico (exists: False)
2025-07-17 10:28:56,874 - root - INFO - Checking for icon at: C:\Users\<USER>\AppData\Local\Temp\_MEI101082\app_icon.ico (exists: False)
2025-07-17 10:28:56,874 - root - INFO - Checking for icon at: D:\PACE_1.1.3\team_distribution\app_icon.ico (exists: False)
2025-07-17 10:28:56,875 - root - WARNING - Could not find application icon
2025-07-17 10:28:58,445 - root - INFO - Main window created and shown successfully
