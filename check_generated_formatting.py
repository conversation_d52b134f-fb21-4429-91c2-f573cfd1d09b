#!/usr/bin/env python3
"""
Check the formatting in our generated Greenberg document to see where italics are coming from.
"""

from docx import Document

def check_generated_formatting():
    """Check the formatting in the generated document"""
    try:
        # Use the most recent test file
        doc_path = "Greenberg_MSA_Garamond_Test_20250718_085217.docx"
        
        print("CHECKING GENERATED GREENBERG DOCUMENT FORMATTING")
        print("=" * 60)
        
        doc = Document(doc_path)
        
        # Check the first paragraph specifically
        first_paragraph = doc.paragraphs[0]
        print(f"FIRST PARAGRAPH:")
        print(f"Text: {first_paragraph.text[:100]}...")
        
        # Check each run in the first paragraph
        for j, run in enumerate(first_paragraph.runs):
            if run.text.strip():
                print(f"\n  Run {j+1}:")
                print(f"    Text: '{run.text[:50]}...'")
                print(f"    Font: {run.font.name}")
                print(f"    Size: {run.font.size}")
                print(f"    Bold: {run.font.bold}")
                print(f"    Italic: {run.font.italic}")
                print(f"    Underline: {run.font.underline}")
                
                # Check if this run has italics
                if run.font.italic:
                    print(f"    ⚠️  ITALIC DETECTED!")
        
        return True
        
    except Exception as e:
        print(f"Error: {e}")
        return False

if __name__ == "__main__":
    check_generated_formatting()
