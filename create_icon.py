#!/usr/bin/env python3
"""
Create a simple PACE icon if one doesn't exist.
"""

import os
from PIL import Image, ImageDraw, ImageFont

def create_pace_icon():
    """Create a simple PACE icon"""
    try:
        # Create a 256x256 image with a blue background
        size = 256
        img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # Draw a blue circle background
        margin = 20
        draw.ellipse([margin, margin, size-margin, size-margin], 
                    fill=(70, 130, 180), outline=(50, 100, 150), width=4)
        
        # Try to use a system font, fall back to default
        try:
            font_size = 60
            font = ImageFont.truetype("arial.ttf", font_size)
        except:
            try:
                font = ImageFont.truetype("calibri.ttf", font_size)
            except:
                font = ImageFont.load_default()
        
        # Draw "PACE" text
        text = "PACE"
        
        # Get text bounding box
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        # Center the text
        x = (size - text_width) // 2
        y = (size - text_height) // 2 - 10
        
        # Draw text with shadow
        draw.text((x+2, y+2), text, fill=(0, 0, 0, 128), font=font)  # Shadow
        draw.text((x, y), text, fill=(255, 255, 255), font=font)     # Main text
        
        # Draw a small subtitle
        try:
            small_font = ImageFont.truetype("arial.ttf", 20)
        except:
            small_font = ImageFont.load_default()
        
        subtitle = "v1.1.4"
        bbox = draw.textbbox((0, 0), subtitle, font=small_font)
        sub_width = bbox[2] - bbox[0]
        sub_x = (size - sub_width) // 2
        sub_y = y + text_height + 10
        
        draw.text((sub_x+1, sub_y+1), subtitle, fill=(0, 0, 0, 128), font=small_font)  # Shadow
        draw.text((sub_x, sub_y), subtitle, fill=(255, 255, 255), font=small_font)     # Main text
        
        # Save as ICO file with multiple sizes
        icon_sizes = [(16, 16), (32, 32), (48, 48), (64, 64), (128, 128), (256, 256)]
        images = []
        
        for icon_size in icon_sizes:
            resized = img.resize(icon_size, Image.Resampling.LANCZOS)
            images.append(resized)
        
        # Save the icon
        images[0].save('PACE.ico', format='ICO', sizes=[img.size for img in images])
        print("✓ Created PACE.ico")
        return True
        
    except Exception as e:
        print(f"✗ Error creating icon: {e}")
        return False

def main():
    """Main function"""
    print("Creating PACE icon...")
    
    # Check if icon already exists
    if os.path.exists('PACE.ico'):
        print("✓ PACE.ico already exists")
        return True
    
    # Try to create icon
    if create_pace_icon():
        print("✓ Icon created successfully")
        return True
    else:
        print("✗ Failed to create icon")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
