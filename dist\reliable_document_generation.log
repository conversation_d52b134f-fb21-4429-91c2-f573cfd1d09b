2025-07-17 12:34:41,959 - root - INFO - Starting PACE application
2025-07-17 12:34:41,961 - root - INFO - Python version: 3.13.5 (tags/v3.13.5:6cb20a2, Jun 11 2025, 16:15:46) [MSC v.1943 64 bit (AMD64)]
2025-07-17 12:34:41,962 - root - INFO - Current directory: D:\PACE_1.1.3\dist
2025-07-17 12:34:41,964 - root - INFO - Script directory: C:\Users\<USER>\AppData\Local\Temp\_MEI459042
2025-07-17 12:34:41,965 - root - INFO - Executable directory: D:\PACE_1.1.3\dist
2025-07-17 12:34:42,313 - root - INFO - Checking for icon at: PACE.ico (exists: False)
2025-07-17 12:34:42,313 - root - INFO - Checking for icon at: C:\Users\<USER>\AppData\Local\Temp\_MEI459042\PACE.ico (exists: False)
2025-07-17 12:34:42,317 - root - INFO - Checking for icon at: D:\PACE_1.1.3\dist\PACE.ico (exists: False)
2025-07-17 12:34:42,318 - root - INFO - Checking for icon at: app_icon.ico (exists: False)
2025-07-17 12:34:42,319 - root - INFO - Checking for icon at: C:\Users\<USER>\AppData\Local\Temp\_MEI459042\app_icon.ico (exists: False)
2025-07-17 12:34:42,321 - root - INFO - Checking for icon at: D:\PACE_1.1.3\dist\app_icon.ico (exists: False)
2025-07-17 12:34:42,322 - root - WARNING - Could not find application icon
2025-07-17 12:34:44,023 - root - INFO - Main window created and shown successfully
2025-07-17 12:40:01,535 - utils.reliable_document_generator - INFO - Using output directory: C:/Users/<USER>/Documents/Test_Pace
2025-07-17 12:40:01,535 - utils.reliable_document_generator - INFO - ReliableDocumentGenerator initialized with output directory: C:/Users/<USER>/Documents/Test_Pace
2025-07-17 12:40:01,535 - utils.reliable_document_generator - INFO - Debugging template paths...
2025-07-17 12:40:01,536 - utils.reliable_document_generator - INFO - SOW template path: templates/base_templates/dfir/SOW_IR_Template_single_price_IR_Investigation.docx
2025-07-17 12:40:01,536 - utils.reliable_document_generator - INFO - Resolved SOW template path: C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates/base_templates/dfir/SOW_IR_Template_single_price_IR_Investigation.docx
2025-07-17 12:40:01,536 - utils.reliable_document_generator - INFO - SOW template exists: True
2025-07-17 12:40:01,536 - utils.reliable_document_generator - INFO - MSA template path: templates/base_templates/msa_templates/Greenberg_Traurig_LLP_Master_Services_Agreement_Template.docx
2025-07-17 12:40:01,536 - utils.reliable_document_generator - INFO - Resolved MSA template path: C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates/base_templates/msa_templates/Greenberg_Traurig_LLP_Master_Services_Agreement_Template.docx
2025-07-17 12:40:01,536 - utils.reliable_document_generator - INFO - MSA template exists: True
2025-07-17 12:40:01,536 - utils.reliable_document_generator - INFO - TACI SOW template path: templates/base_templates/taci/SOW_Ransom_Consulting_template.docx
2025-07-17 12:40:01,537 - utils.reliable_document_generator - INFO - Resolved TACI SOW template path: C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates/base_templates/taci/SOW_Ransom_Consulting_template.docx
2025-07-17 12:40:01,537 - utils.reliable_document_generator - INFO - TACI SOW template exists: True
2025-07-17 12:40:01,537 - utils.reliable_document_generator - INFO - RR SOW template path: templates/base_templates/rr/SOW_ON_Template_Recovery_&_Restoration_Support.docx
2025-07-17 12:40:01,537 - utils.reliable_document_generator - INFO - Resolved RR SOW template path: C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates/base_templates/rr/SOW_ON_Template_Recovery_&_Restoration_Support.docx
2025-07-17 12:40:01,537 - utils.reliable_document_generator - INFO - RR SOW template exists: True
2025-07-17 12:40:01,537 - utils.reliable_document_generator - INFO - All available templates:
2025-07-17 12:40:01,539 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\baa\Template_Business_Associate_Agreement.docx
2025-07-17 12:40:01,540 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\bec\SOW_Baker_GCP_BEC_template.docx
2025-07-17 12:40:01,540 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\bec\SOW_Baker_M365_BEC_template.docx
2025-07-17 12:40:01,544 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\bec\SOW_Chubb_Exchange_BEC_template.docx
2025-07-17 12:40:01,545 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\bec\SOW_Chubb_GCP_BEC_template.docx
2025-07-17 12:40:01,545 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\bec\SOW_Chubb_M365_BEC_template.docx
2025-07-17 12:40:01,545 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\bec\SOW_Coalition_GCP_BEC_template.docx
2025-07-17 12:40:01,545 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\bec\SOW_Coalition_M365_BEC_template.docx
2025-07-17 12:40:01,545 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\bec\SOW_Dykema_GCP_BEC_template.docx
2025-07-17 12:40:01,546 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\bec\SOW_Dykema_M365_BEC_template.docx
2025-07-17 12:40:01,546 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\bec\SOW_Exchange_BEC_template.docx
2025-07-17 12:40:01,547 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\bec\SOW_GCP_BEC_template.docx
2025-07-17 12:40:01,547 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\bec\SOW_M365_BEC_template.docx
2025-07-17 12:40:01,547 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\bec\SOW_Woods_M365_BEC_template.docx
2025-07-17 12:40:01,548 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\dfir\SOW_chubb_IR_cs_codes_template.docx
2025-07-17 12:40:01,548 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\dfir\SOW_Fixed_Fee_IR_template.docx
2025-07-17 12:40:01,549 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\dfir\SOW_IR_BRAP_Beckage_Verbiage_Template.docx
2025-07-17 12:40:01,549 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\dfir\SOW_IR_Template_IR Investigation_Beckage_Template.docx
2025-07-17 12:40:01,549 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\dfir\SOW_IR_Template_single_price_IR_Investigation.docx
2025-07-17 12:40:01,549 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\dfir\SOW_IR_Template_Tokio-Marine_Single Price_IR Investigation.docx
2025-07-17 12:40:01,550 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\dfir\SOW_IR_Template_Woods_Rogers_Only_IR_Investigation.docx
2025-07-17 12:40:01,550 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\dfir\SOW__IR_template_Beazley_FFP.docx
2025-07-17 12:40:01,550 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\dfir\SOW__IR_template_Beazley_FFP_EDR_New_Hybrid.docx
2025-07-17 12:40:01,550 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\dfir\SOW__IR_template_Beazley_FFP_EDR_New_M365.docx
2025-07-17 12:40:01,550 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\dpa\Template_Data_Processing_Agreement.docx
2025-07-17 12:40:01,551 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\msa_templates\Baker_&_Hostetler_LLP_(2-Party)_Master_Services_Agreement_Template.docx
2025-07-17 12:40:01,551 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\msa_templates\Baker_&_Hostetler_LLP_Beazley_TASB_Master_Services_Agreement_Template.docx
2025-07-17 12:40:01,552 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\msa_templates\Baker_Donelson_Bearman_Caldwell_&_Berkowitz_P.C._Master_Services_Agreement_Template.docx
2025-07-17 12:40:01,552 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\msa_templates\Cipriani_&_Werner_PC_Master_Services_Agreement_Template.docx
2025-07-17 12:40:01,552 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\msa_templates\Clark_Hill_PLC_Master_Services_Agreement.docx
2025-07-17 12:40:01,552 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\msa_templates\Constangy_Brooks_Smith_&_Prophete_LLP_Master_Services_Agreement_Template.docx
2025-07-17 12:40:01,552 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\msa_templates\Dykema_Gossett_PLLC_Master_Service_Agreement_Template.docx
2025-07-17 12:40:01,553 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\msa_templates\Eckert_Seamans_Cherin_&_Mellott_LLC_Master_Service_Agreement_Template.docx
2025-07-17 12:40:01,553 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\msa_templates\Gordon_Rees_Scully_Mansukhani_LLP_Master_Services_Agreement_Template.docx
2025-07-17 12:40:01,553 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\msa_templates\Greenberg_Traurig_LLP_Master_Services_Agreement_Template.docx
2025-07-17 12:40:01,554 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\msa_templates\IR_Services_2_Party_Master_Services_Agreement_Template.docx
2025-07-17 12:40:01,554 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\msa_templates\IR_Services_3_Party_Master_Services_Agreement_Template.docx
2025-07-17 12:40:01,554 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\msa_templates\Jackson_Lewis_PC_Master_Services_Agreement_Template.docx
2025-07-17 12:40:01,554 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\msa_templates\Jackson_Lewis_PC_with_BAA_Master_Services_Agreement_Template.docx
2025-07-17 12:40:01,554 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\msa_templates\Lewis_Brisbois_Bisgaard_&_Smith_LLP_Master_Services_Agreement_Template.docx
2025-07-17 12:40:01,555 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\msa_templates\Locke_Lord_Master_Services_Agreement_Template.docx
2025-07-17 12:40:01,555 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\msa_templates\Maynard_Nexsen_PC_Master_Services_Agreement_Template.docx
2025-07-17 12:40:01,555 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\msa_templates\McDonald_Hopkins_LLC_Master_Services_Agreement_Template.docx
2025-07-17 12:40:01,555 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\msa_templates\Mullen_Coughlin_LLC_Master_Services_Agreement_Template.docx
2025-07-17 12:40:01,555 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\msa_templates\Nelson_Mullins_Riley_&_Scarborough_LLP_Master_Services_Agreement_Template.docx
2025-07-17 12:40:01,555 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\msa_templates\Norton_Rose_Fulbright_Master_Services_Agreement_Template.docx
2025-07-17 12:40:01,555 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\msa_templates\Octillo_Law_PLLC_Master_Services_Agreement_Template.docx
2025-07-17 12:40:01,556 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\msa_templates\Ogletree_Deakins_Master_Services_Agreement_Template.docx
2025-07-17 12:40:01,556 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\msa_templates\Ruskin_Moscou_Faltischek_P.C._Master_Services_Agreement_Template.docx
2025-07-17 12:40:01,556 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\msa_templates\Shook_Hardy_&_Bacon_LLP_Master_Services_Agreement_Template.docx
2025-07-17 12:40:01,556 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\msa_templates\The_Beckage_Firm_PLLC_Master_Services_Agreement_Template.docx
2025-07-17 12:40:01,556 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\msa_templates\Woods_Rogers_Master_Services_Agreement_Template.docx
2025-07-17 12:40:01,556 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\rr\SOW_ON_Template_Beazley_Only_Recovery_&_Restoration_Support.docx
2025-07-17 12:40:01,556 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\rr\SOW_ON_Template_Recovery_&_Restoration_Support.docx
2025-07-17 12:40:01,556 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\taci\SOW_Coalition_Ransom_Consulting_FFP_template.docx
2025-07-17 12:40:01,557 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\taci\SOW_Ransom_Consulting_template.docx
2025-07-17 12:40:01,557 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\taci\SOW_RN-Template_Ransom_Site_Monitoring.docx
2025-07-17 12:40:01,557 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\taci\SOW_RN_Template_Beckage_Firm_Ransom_Consulting.docx
2025-07-17 12:40:01,557 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\taci\SOW_RN_Template_Chubb_Only_Ransom_Consulting.docx
2025-07-17 12:40:01,557 - utils.reliable_document_generator - INFO -   - C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates\base_templates\taci\SOW_RN_Template_Client_Data_Download.docx
2025-07-17 12:40:01,559 - utils.reliable_document_generator - INFO - Generating documents for DFIR engagement
2025-07-17 12:40:01,560 - utils.reliable_document_generator - INFO - Client: test100
2025-07-17 12:40:01,560 - utils.reliable_document_generator - INFO - Law Firm: Greenberg Traurig, LLP
2025-07-17 12:40:01,560 - utils.reliable_document_generator - INFO - Output directory: C:/Users/<USER>/Documents/Test_Pace
2025-07-17 12:40:01,560 - utils.reliable_document_generator - INFO - Generating SOW...
2025-07-17 12:40:01,560 - utils.reliable_document_generator - INFO - SOW template path: templates/base_templates/dfir/SOW_IR_Template_single_price_IR_Investigation.docx
2025-07-17 12:40:01,561 - utils.reliable_document_generator - INFO - SOW output path: C:/Users/<USER>/Documents/Test_Pace\test100 IR Investigation - SOW 20250717.docx
2025-07-17 12:40:01,562 - utils.reliable_document_generator - INFO - Using python-docx to replace placeholders in SOW document
2025-07-17 12:40:01,562 - utils.docx_placeholder_replacer - INFO - Resolved template path: C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates/base_templates/dfir/SOW_IR_Template_single_price_IR_Investigation.docx
2025-07-17 12:40:01,563 - utils.docx_placeholder_replacer - INFO - Loading document: C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates/base_templates/dfir/SOW_IR_Template_single_price_IR_Investigation.docx
2025-07-17 12:40:02,268 - utils.docx_placeholder_replacer - INFO - Saving document to: C:/Users/<USER>/Documents/Test_Pace\test100 IR Investigation - SOW 20250717.docx
2025-07-17 12:40:02,290 - utils.docx_placeholder_replacer - INFO - Document generated successfully: C:/Users/<USER>/Documents/Test_Pace\test100 IR Investigation - SOW 20250717.docx
2025-07-17 12:40:02,290 - utils.reliable_document_generator - INFO - SOW document created successfully with python-docx: C:/Users/<USER>/Documents/Test_Pace\test100 IR Investigation - SOW 20250717.docx
2025-07-17 12:40:02,290 - utils.reliable_document_generator - INFO - TACI SOW is needed
2025-07-17 12:40:02,290 - utils.reliable_document_generator - INFO - Generating TACI SOW...
2025-07-17 12:40:02,290 - utils.reliable_document_generator - INFO - TACI SOW template path: templates/base_templates/taci/SOW_Ransom_Consulting_template.docx
2025-07-17 12:40:02,291 - utils.reliable_document_generator - INFO - TACI SOW output path: C:/Users/<USER>/Documents/Test_Pace\test100 Ransom Communications - SOW 20250717.docx
2025-07-17 12:40:02,291 - utils.reliable_document_generator - INFO - Using python-docx to replace placeholders in TACI SOW document
2025-07-17 12:40:02,291 - utils.docx_placeholder_replacer - INFO - Resolved template path: C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates/base_templates/taci/SOW_Ransom_Consulting_template.docx
2025-07-17 12:40:02,292 - utils.docx_placeholder_replacer - INFO - Loading document: C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates/base_templates/taci/SOW_Ransom_Consulting_template.docx
2025-07-17 12:40:02,769 - utils.docx_placeholder_replacer - INFO - Saving document to: C:/Users/<USER>/Documents/Test_Pace\test100 Ransom Communications - SOW 20250717.docx
2025-07-17 12:40:06,510 - utils.docx_placeholder_replacer - INFO - Document generated successfully: C:/Users/<USER>/Documents/Test_Pace\test100 Ransom Communications - SOW 20250717.docx
2025-07-17 12:40:06,510 - utils.reliable_document_generator - INFO - TACI SOW document created successfully with python-docx: C:/Users/<USER>/Documents/Test_Pace\test100 Ransom Communications - SOW 20250717.docx
2025-07-17 12:40:06,510 - utils.reliable_document_generator - INFO - RR SOW document generation requested
2025-07-17 12:40:06,510 - utils.reliable_document_generator - INFO - Generating RR SOW...
2025-07-17 12:40:06,510 - utils.reliable_document_generator - INFO - RR SOW template path: templates/base_templates/rr/SOW_ON_Template_Recovery_&_Restoration_Support.docx
2025-07-17 12:40:06,511 - utils.reliable_document_generator - INFO - RR SOW output path: C:/Users/<USER>/Documents/Test_Pace\test100 Recovery & Remediation - SOW 20250717.docx
2025-07-17 12:40:06,511 - utils.reliable_document_generator - INFO - Using python-docx to replace placeholders in RR SOW document
2025-07-17 12:40:06,511 - utils.docx_placeholder_replacer - INFO - Resolved template path: C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates/base_templates/rr/SOW_ON_Template_Recovery_&_Restoration_Support.docx
2025-07-17 12:40:06,512 - utils.docx_placeholder_replacer - INFO - Loading document: C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates/base_templates/rr/SOW_ON_Template_Recovery_&_Restoration_Support.docx
2025-07-17 12:40:06,892 - utils.docx_placeholder_replacer - INFO - Saving document to: C:/Users/<USER>/Documents/Test_Pace\test100 Recovery & Remediation - SOW 20250717.docx
2025-07-17 12:40:06,903 - utils.docx_placeholder_replacer - INFO - Document generated successfully: C:/Users/<USER>/Documents/Test_Pace\test100 Recovery & Remediation - SOW 20250717.docx
2025-07-17 12:40:06,903 - utils.reliable_document_generator - INFO - RR SOW document created successfully with python-docx: C:/Users/<USER>/Documents/Test_Pace\test100 Recovery & Remediation - SOW 20250717.docx
2025-07-17 12:40:06,904 - utils.reliable_document_generator - INFO - Generating MSA...
2025-07-17 12:40:06,904 - utils.reliable_document_generator - INFO - MSA template path: templates/base_templates/msa_templates/Greenberg_Traurig_LLP_Master_Services_Agreement_Template.docx
2025-07-17 12:40:06,904 - utils.reliable_document_generator - INFO - MSA output path: C:/Users/<USER>/Documents/Test_Pace\test100 Master Services Agreement (MSA) 20250717.docx
2025-07-17 12:40:06,905 - utils.reliable_document_generator - INFO - Using python-docx to replace placeholders in MSA document
2025-07-17 12:40:06,905 - utils.docx_placeholder_replacer - INFO - Resolved template path: C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates/base_templates/msa_templates/Greenberg_Traurig_LLP_Master_Services_Agreement_Template.docx
2025-07-17 12:40:06,906 - utils.docx_placeholder_replacer - INFO - Loading document: C:\Users\<USER>\AppData\Local\Temp\_MEI459042\templates/base_templates/msa_templates/Greenberg_Traurig_LLP_Master_Services_Agreement_Template.docx
2025-07-17 12:40:07,391 - utils.docx_placeholder_replacer - INFO - Saving document to: C:/Users/<USER>/Documents/Test_Pace\test100 Master Services Agreement (MSA) 20250717.docx
2025-07-17 12:40:07,411 - utils.docx_placeholder_replacer - INFO - Document generated successfully: C:/Users/<USER>/Documents/Test_Pace\test100 Master Services Agreement (MSA) 20250717.docx
2025-07-17 12:40:07,411 - utils.reliable_document_generator - INFO - MSA document created successfully with python-docx: C:/Users/<USER>/Documents/Test_Pace\test100 Master Services Agreement (MSA) 20250717.docx
2025-07-17 12:40:07,411 - utils.reliable_document_generator - INFO - Generated 4 documents:
2025-07-17 12:40:07,412 - utils.reliable_document_generator - INFO - Document 1: C:/Users/<USER>/Documents/Test_Pace\test100 IR Investigation - SOW 20250717.docx
2025-07-17 12:40:07,412 - utils.reliable_document_generator - INFO - Document 2: C:/Users/<USER>/Documents/Test_Pace\test100 Ransom Communications - SOW 20250717.docx
2025-07-17 12:40:07,412 - utils.reliable_document_generator - INFO - Document 3: C:/Users/<USER>/Documents/Test_Pace\test100 Recovery & Remediation - SOW 20250717.docx
2025-07-17 12:40:07,412 - utils.reliable_document_generator - INFO - Document 4: C:/Users/<USER>/Documents/Test_Pace\test100 Master Services Agreement (MSA) 20250717.docx
